#!/bin/bash

# More Than Scripts - Setup Script
# This script sets up the development environment for the More Than Scripts application

echo "🎬 Setting up More Than Scripts..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3 and try again."
    exit 1
fi

echo "✅ Python 3 found"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "📥 Installing Python dependencies..."
cd backend
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Failed to install Python dependencies"
    exit 1
fi
cd ..

echo "✅ Python dependencies installed"

# Create databases directory if it doesn't exist
if [ ! -d "databases" ]; then
    mkdir databases
    echo "✅ Created databases directory"
fi

# Run tests to verify setup
echo "🧪 Running API tests..."
cd backend
python test_api.py &
TEST_PID=$!

# Start the Flask server in the background
echo "🚀 Starting Flask server..."
python app.py &
SERVER_PID=$!

# Wait a moment for server to start
sleep 3

# Kill the test process if it's still running
if ps -p $TEST_PID > /dev/null; then
    kill $TEST_PID 2>/dev/null
fi

# Run the actual tests
python test_api.py
TEST_RESULT=$?

# Kill the server
kill $SERVER_PID 2>/dev/null

cd ..

if [ $TEST_RESULT -eq 0 ]; then
    echo "✅ All tests passed!"
else
    echo "❌ Some tests failed. Please check the output above."
    exit 1
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "To start the application:"
echo "1. Activate the virtual environment: source venv/bin/activate"
echo "2. Start the server: cd backend && python app.py"
echo "3. Open your browser to: http://localhost:5000"
echo ""
echo "📚 For more information, see README.md"
