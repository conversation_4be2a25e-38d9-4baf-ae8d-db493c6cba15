#!/bin/bash

# Deploy MySQL for scriptwriter application
echo "🚀 Deploying MySQL for scriptwriter application..."

# Stop and remove existing container if it exists
echo "🛑 Stopping existing MySQL container..."
docker stop scriptwriter-mysql 2>/dev/null || true
docker rm scriptwriter-mysql 2>/dev/null || true

# Create Docker network if it doesn't exist
echo "🌐 Creating Docker network..."
docker network create scriptwriter-network 2>/dev/null || true

# Start MySQL container
echo "🐳 Starting MySQL container..."
docker run -d \
  --name scriptwriter-mysql \
  --network scriptwriter-network \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD="This is a 1 horse town!" \
  -e MYSQL_DATABASE=scriptwriter \
  -e MYSQL_USER=scriptwriter_api \
  -e MYSQL_PASSWORD=scriptwriter_pass123 \
  -v scriptwriter_mysql_data:/var/lib/mysql \
  --restart unless-stopped \
  mysql:8.0

# Wait for MySQL to be ready
echo "⏳ Waiting for MySQL to be ready..."
sleep 30

# Test connection
echo "🔍 Testing MySQL connection..."
docker exec scriptwriter-mysql mysql -u root -p"This is a 1 horse town!" -e "SHOW DATABASES;"

if [ $? -eq 0 ]; then
    echo "✅ MySQL deployed successfully!"
    echo ""
    echo "📋 Connection Details:"
    echo "   Host: localhost"
    echo "   Port: 3306"
    echo "   Database: scriptwriter"
    echo "   API User: scriptwriter_api"
    echo "   API Password: scriptwriter_pass123"
    echo "   Root Password: This is a 1 horse town!"
    echo ""
    echo "🔧 To connect to MySQL shell:"
    echo "   docker exec -it scriptwriter-mysql mysql -u root -p"
else
    echo "❌ MySQL deployment failed!"
    exit 1
fi
