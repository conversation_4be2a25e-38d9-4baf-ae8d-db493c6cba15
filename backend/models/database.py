import mysql.connector
import os
from datetime import datetime
import json

class DatabaseManager:
    def __init__(self, user_id):
        self.user_id = user_id
        self.init_database()

    def get_connection(self):
        return mysql.connector.connect(
            host='***********',
            port=3306,
            user='scriptwriter_api',
            password='scriptwriter_pass123',
            database='scriptwriter',
            autocommit=False
        )
    
    def init_database(self):
        """Initialize the database with all required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Projects table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_user_id (user_id)
                )
            ''')
            
            # Scripts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS scripts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_id INT NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    content LONGTEXT,
                    format_type VARCHAR(50) DEFAULT 'screenplay',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
                    INDEX idx_project_id (project_id)
                )
            ''')

            # Script elements table for structured script content
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS script_elements (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    script_id INT NOT NULL,
                    element_type ENUM('act', 'scene', 'transition', 'shot', 'action', 'character', 'dialog', 'parenthetical') NOT NULL,
                    order_index INT NOT NULL,
                    content TEXT NOT NULL,
                    character_name VARCHAR(255) NULL,
                    character_id INT NULL,
                    metadata JSON NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (script_id) REFERENCES scripts (id) ON DELETE CASCADE,
                    FOREIGN KEY (character_id) REFERENCES cast (id) ON DELETE SET NULL,
                    INDEX idx_script_id (script_id),
                    INDEX idx_order (script_id, order_index),
                    INDEX idx_character (character_id)
                )
            ''')
            
            # Shot lists table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS shot_lists (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    script_id INT NOT NULL,
                    scene_number VARCHAR(50),
                    shot_number VARCHAR(50),
                    shot_type VARCHAR(100),
                    description TEXT,
                    camera_angle VARCHAR(100),
                    movement VARCHAR(100),
                    audio_notes TEXT,
                    duration_estimate INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (script_id) REFERENCES scripts (id) ON DELETE CASCADE,
                    INDEX idx_script_id (script_id)
                )
            ''')
            
            # Cast table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cast (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    character_name VARCHAR(255),
                    email VARCHAR(255),
                    phone VARCHAR(50),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
                    INDEX idx_project_id (project_id)
                )
            ''')
            
            # Crew table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crew (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    role VARCHAR(255) NOT NULL,
                    email VARCHAR(255),
                    phone VARCHAR(50),
                    department VARCHAR(255),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
                    INDEX idx_project_id (project_id)
                )
            ''')
            
            # Shooting schedule table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS shooting_schedule (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_id INT NOT NULL,
                    scene_number VARCHAR(50),
                    location VARCHAR(255),
                    shoot_date DATE,
                    start_time TIME,
                    end_time TIME,
                    cast_required TEXT,
                    crew_required TEXT,
                    equipment_needed TEXT,
                    notes TEXT,
                    status VARCHAR(50) DEFAULT 'planned',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
                    INDEX idx_project_id (project_id)
                )
            ''')
            
            # Storyboards table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS storyboards (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    project_id INT NOT NULL,
                    scene_number VARCHAR(50),
                    panel_number INT,
                    description TEXT,
                    image_path VARCHAR(500),
                    dialogue TEXT,
                    camera_notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
                    INDEX idx_project_id (project_id)
                )
            ''')

            conn.commit()
    
    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
        finally:
            conn.close()

    def execute_insert(self, query, params=None):
        """Execute an insert query and return the last row id"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()

    def execute_update(self, query, params=None):
        """Execute an update/delete query and return affected rows"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()
