import mysql.connector
import bcrypt
import os
from datetime import datetime

class AuthManager:
    def __init__(self):
        self.init_auth_database()

    def get_connection(self):
        return mysql.connector.connect(
            host='***********',
            port=3306,
            user='scriptwriter_api',
            password='scriptwriter_pass123',
            database='scriptwriter',
            autocommit=False
        )
    
    def init_auth_database(self):
        """Initialize the main authentication database"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()

            # Users table for authentication
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(255) UNIQUE NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''')

            # Sessions table for session management
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id VARCHAR(255) PRIMARY KEY,
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            ''')

            conn.commit()
        finally:
            conn.close()
    
    def hash_password(self, password):
        """Hash a password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password, password_hash):
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def register_user(self, username, email, password):
        """Register a new user"""
        try:
            password_hash = self.hash_password(password)

            conn = self.get_connection()
            try:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO users (username, email, password_hash)
                    VALUES (%s, %s, %s)
                ''', (username, email, password_hash))
                conn.commit()
                return cursor.lastrowid
            finally:
                conn.close()
        except mysql.connector.IntegrityError:
            return None  # User already exists
    
    def authenticate_user(self, username, password):
        """Authenticate a user and return user info if successful"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, username, email, password_hash
                FROM users
                WHERE username = %s AND is_active = TRUE
            ''', (username,))

            user = cursor.fetchone()
            if user and self.verify_password(password, user[3]):
                # Update last login
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP
                    WHERE id = %s
                ''', (user[0],))
                conn.commit()

                return {
                    'id': user[0],
                    'username': user[1],
                    'email': user[2]
                }
            return None
        finally:
            conn.close()
    
    def get_user_by_id(self, user_id):
        """Get user information by ID"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, username, email
                FROM users
                WHERE id = %s AND is_active = TRUE
            ''', (user_id,))

            user = cursor.fetchone()
            if user:
                return {
                    'id': user[0],
                    'username': user[1],
                    'email': user[2]
                }
            return None
        finally:
            conn.close()

    def create_session(self, user_id, session_id, expires_at):
        """Create a new session"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sessions (id, user_id, expires_at)
                VALUES (%s, %s, %s)
            ''', (session_id, user_id, expires_at))
            conn.commit()
        finally:
            conn.close()
    
    def get_session(self, session_id):
        """Get session information"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT s.user_id, u.username, u.email
                FROM sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.id = %s AND s.expires_at > CURRENT_TIMESTAMP
            ''', (session_id,))

            session = cursor.fetchone()
            if session:
                return {
                    'user_id': session[0],
                    'username': session[1],
                    'email': session[2]
                }
            return None
        finally:
            conn.close()

    def delete_session(self, session_id):
        """Delete a session (logout)"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM sessions WHERE id = %s', (session_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()

    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM sessions WHERE expires_at <= CURRENT_TIMESTAMP')
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()
