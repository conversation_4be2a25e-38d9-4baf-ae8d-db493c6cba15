from flask import Flask, request, jsonify, session, send_from_directory
from flask_cors import CORS
import os
import secrets
from datetime import datetime, timedelta
import json

from models.auth import AuthManager
from models.database import DatabaseManager

# Check if we're in production
IS_PRODUCTION = os.path.exists('/root/thisisprod.txt')

app = Flask(__name__)
app.secret_key = secrets.token_hex(32)  # Generate a secure secret key

if IS_PRODUCTION:
    app.config['SESSION_COOKIE_SAMESITE'] = 'None'
    app.config['SESSION_COOKIE_SECURE'] = True
    # Production CORS origins
    cors_origins = ['https://api.morethanscripts.com', 'https://morethanscripts.com']
else:
    # Development CORS origins
    cors_origins = ['http://localhost:5000', 'http://localhost:8080', 'http://127.0.0.1:5000', 'http://127.0.0.1:8080']

# Enable CORS for frontend communication
CORS(app, supports_credentials=True, origins=cors_origins)

# Blueprints will be registered after they are imported

# Initialize authentication manager
auth_manager = AuthManager()

def require_auth(f):
    """Decorator to require authentication for routes"""
    def decorated_function(*args, **kwargs):
        session_id = session.get('session_id')
        print(f"DEBUG: Session ID from cookie: {session_id}")
        print(f"DEBUG: All session data: {dict(session)}")

        if not session_id:
            return jsonify({'error': 'Authentication required - no session'}), 401

        user_session = auth_manager.get_session(session_id)
        if not user_session:
            session.clear()
            return jsonify({'error': 'Invalid or expired session'}), 401

        # Add user info to request context
        request.user = user_session
        return f(*args, **kwargs)

    decorated_function.__name__ = f.__name__
    return decorated_function

def get_user_db():
    """Get database manager for current user"""
    return DatabaseManager(request.user['user_id'])

# Serve static files
@app.route('/')
def serve_frontend():
    return send_from_directory('../frontend', 'login.html')

@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory('../frontend', path)

# Authentication routes
@app.route('/auth/register', methods=['POST'])
def register():
    data = request.get_json()
    
    if not data or not all(k in data for k in ('username', 'email', 'password')):
        return jsonify({'error': 'Missing required fields'}), 400
    
    username = data['username'].strip()
    email = data['email'].strip()
    password = data['password']
    
    if len(username) < 3 or len(password) < 6:
        return jsonify({'error': 'Username must be at least 3 characters, password at least 6'}), 400
    
    user_id = auth_manager.register_user(username, email, password)
    if user_id:
        return jsonify({'message': 'User registered successfully', 'user_id': user_id}), 201
    else:
        return jsonify({'error': 'Username or email already exists'}), 409

@app.route('/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    
    if not data or not all(k in data for k in ('username', 'password')):
        return jsonify({'error': 'Missing username or password'}), 400
    
    user = auth_manager.authenticate_user(data['username'], data['password'])
    if user:
        # Create session
        session_id = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(days=7)  # 7 day session
        
        auth_manager.create_session(user['id'], session_id, expires_at)
        session['session_id'] = session_id
        print(f"DEBUG: Created session {session_id} for user {user['id']}")
        print(f"DEBUG: Session data after login: {dict(session)}")

        return jsonify({
            'message': 'Login successful',
            'user': user
        }), 200
    else:
        return jsonify({'error': 'Invalid username or password'}), 401

@app.route('/auth/logout', methods=['POST'])
@require_auth
def logout():
    session_id = session.get('session_id')
    if session_id:
        auth_manager.delete_session(session_id)
    session.clear()
    return jsonify({'message': 'Logged out successfully'}), 200

@app.route('/auth/me', methods=['GET'])
@require_auth
def get_current_user():
    return jsonify({'user': request.user}), 200

# Project management routes
@app.route('/projects', methods=['GET'])
@require_auth
def get_projects():
    db = get_user_db()
    projects = db.execute_query('SELECT * FROM projects WHERE user_id = %s ORDER BY updated_at DESC', (request.user['user_id'],))

    project_list = []
    for project in projects:
        project_list.append({
            'id': project[0],
            'user_id': project[1],
            'name': project[2],
            'description': project[3],
            'created_at': project[4],
            'updated_at': project[5]
        })

    return jsonify({'projects': project_list}), 200

@app.route('/projects', methods=['POST'])
@require_auth
def create_project():
    data = request.get_json()

    if not data or 'name' not in data:
        return jsonify({'error': 'Project name is required'}), 400

    db = get_user_db()
    project_id = db.execute_insert(
        'INSERT INTO projects (user_id, name, description) VALUES (%s, %s, %s)',
        (request.user['user_id'], data['name'], data.get('description', ''))
    )

    return jsonify({
        'message': 'Project created successfully',
        'project_id': project_id
    }), 201

@app.route('/projects/<int:project_id>', methods=['GET'])
@require_auth
def get_project(project_id):
    db = get_user_db()
    project = db.execute_query('SELECT * FROM projects WHERE id = %s', (project_id,))
    
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    project = project[0]
    return jsonify({
        'project': {
            'id': project[0],
            'name': project[1],
            'description': project[2],
            'created_at': project[3],
            'updated_at': project[4]
        }
    }), 200

@app.route('/projects/<int:project_id>', methods=['PUT'])
@require_auth
def update_project(project_id):
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    db = get_user_db()
    
    # Check if project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = %s', (project_id,))
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    # Update project
    update_fields = []
    params = []
    
    if 'name' in data:
        update_fields.append('name = %s')
        params.append(data['name'])
    
    if 'description' in data:
        update_fields.append('description = %s')
        params.append(data['description'])
    
    if update_fields:
        update_fields.append('updated_at = CURRENT_TIMESTAMP')
        params.append(project_id)
        
        query = f'UPDATE projects SET {", ".join(update_fields)} WHERE id = %s'
        db.execute_update(query, params)
    
    return jsonify({'message': 'Project updated successfully'}), 200

@app.route('/projects/<int:project_id>', methods=['DELETE'])
@require_auth
def delete_project(project_id):
    db = get_user_db()
    
    rows_affected = db.execute_update('DELETE FROM projects WHERE id = %s', (project_id,))
    
    if rows_affected > 0:
        return jsonify({'message': 'Project deleted successfully'}), 200
    else:
        return jsonify({'error': 'Project not found'}), 404

# Script management routes
@app.route('/scripts', methods=['GET'])
@require_auth
def get_scripts():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400

    db = get_user_db()
    scripts = db.execute_query(
        'SELECT * FROM scripts WHERE project_id = %s ORDER BY updated_at DESC',
        (project_id,)
    )

    script_list = []
    for script in scripts:
        script_list.append({
            'id': script[0],
            'project_id': script[1],
            'title': script[2],
            'content': script[3],
            'format_type': script[4],
            'created_at': script[5],
            'updated_at': script[6]
        })

    return jsonify({'scripts': script_list}), 200

@app.route('/scripts', methods=['POST'])
@require_auth
def create_script():
    data = request.get_json()

    if not data or not all(k in data for k in ('project_id', 'title')):
        return jsonify({'error': 'project_id and title are required'}), 400

    db = get_user_db()

    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = %s', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    script_id = db.execute_insert(
        'INSERT INTO scripts (project_id, title, content, format_type) VALUES (%s, %s, %s, %s)',
        (data['project_id'], data['title'], data.get('content', ''), data.get('format_type', 'screenplay'))
    )

    return jsonify({
        'message': 'Script created successfully',
        'script_id': script_id
    }), 201

@app.route('/scripts/<int:script_id>', methods=['GET'])
@require_auth
def get_script(script_id):
    db = get_user_db()
    script = db.execute_query('''
        SELECT s.*, p.name as project_name
        FROM scripts s
        JOIN projects p ON s.project_id = p.id
        WHERE s.id = %s AND p.user_id = %s
    ''', (script_id, request.user['user_id']))

    if not script:
        return jsonify({'error': 'Script not found'}), 404

    script = script[0]
    return jsonify({
        'script': {
            'id': script[0],
            'project_id': script[1],
            'title': script[2],
            'content': script[3],
            'format_type': script[4],
            'created_at': script[5],
            'updated_at': script[6],
            'project_name': script[7]
        }
    }), 200

@app.route('/scripts/<int:script_id>', methods=['PUT'])
@require_auth
def update_script(script_id):
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    db = get_user_db()

    # Check if script exists
    script = db.execute_query('SELECT id FROM scripts WHERE id = %s', (script_id,))
    if not script:
        return jsonify({'error': 'Script not found'}), 404

    # Update script
    update_fields = []
    params = []

    for field in ['title', 'content', 'format_type']:
        if field in data:
            update_fields.append(f'{field} = %s')
            params.append(data[field])

    if update_fields:
        update_fields.append('updated_at = CURRENT_TIMESTAMP')
        params.append(script_id)

        query = f'UPDATE scripts SET {", ".join(update_fields)} WHERE id = %s'
        db.execute_update(query, params)

    return jsonify({'message': 'Script updated successfully'}), 200

@app.route('/scripts/<int:script_id>', methods=['DELETE'])
@require_auth
def delete_script(script_id):
    db = get_user_db()

    rows_affected = db.execute_update('DELETE FROM scripts WHERE id = %s', (script_id,))

    if rows_affected > 0:
        return jsonify({'message': 'Script deleted successfully'}), 200
    else:
        return jsonify({'error': 'Script not found'}), 404

# Shot list management routes
@app.route('/shotlists', methods=['GET'])
@require_auth
def get_shot_lists():
    script_id = request.args.get('script_id')
    if not script_id:
        return jsonify({'error': 'script_id parameter is required'}), 400

    db = get_user_db()
    shots = db.execute_query(
        'SELECT * FROM shot_lists WHERE script_id = %s ORDER BY scene_number, shot_number',
        (script_id,)
    )

    shot_list = []
    for shot in shots:
        shot_list.append({
            'id': shot[0],
            'script_id': shot[1],
            'scene_number': shot[2],
            'shot_number': shot[3],
            'shot_type': shot[4],
            'description': shot[5],
            'camera_angle': shot[6],
            'movement': shot[7],
            'audio_notes': shot[8],
            'duration_estimate': shot[9],
            'created_at': shot[10]
        })

    return jsonify({'shot_lists': shot_list}), 200

@app.route('/shotlists', methods=['POST'])
@require_auth
def create_shot():
    data = request.get_json()

    if not data or not all(k in data for k in ('script_id', 'scene_number', 'shot_number')):
        return jsonify({'error': 'script_id, scene_number, and shot_number are required'}), 400

    db = get_user_db()

    # Verify script exists
    script = db.execute_query('SELECT id FROM scripts WHERE id = %s', (data['script_id'],))
    if not script:
        return jsonify({'error': 'Script not found'}), 404

    shot_id = db.execute_insert('''
        INSERT INTO shot_lists (script_id, scene_number, shot_number, shot_type,
                               description, camera_angle, movement, audio_notes, duration_estimate)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    ''', (
        data['script_id'],
        data['scene_number'],
        data['shot_number'],
        data.get('shot_type', ''),
        data.get('description', ''),
        data.get('camera_angle', ''),
        data.get('movement', ''),
        data.get('audio_notes', ''),
        data.get('duration_estimate', 0)
    ))

    return jsonify({
        'message': 'Shot created successfully',
        'shot_id': shot_id
    }), 201

@app.route('/shotlists/<int:shot_id>', methods=['PUT'])
@require_auth
def update_shot(shot_id):
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    db = get_user_db()

    # Check if shot exists
    shot = db.execute_query('SELECT id FROM shot_lists WHERE id = %s', (shot_id,))
    if not shot:
        return jsonify({'error': 'Shot not found'}), 404

    # Update shot
    update_fields = []
    params = []

    fields = ['scene_number', 'shot_number', 'shot_type', 'description',
              'camera_angle', 'movement', 'audio_notes', 'duration_estimate']

    for field in fields:
        if field in data:
            update_fields.append(f'{field} = %s')
            params.append(data[field])

    if update_fields:
        params.append(shot_id)
        query = f'UPDATE shot_lists SET {", ".join(update_fields)} WHERE id = %s'
        db.execute_update(query, params)

    return jsonify({'message': 'Shot updated successfully'}), 200

@app.route('/shotlists/<int:shot_id>', methods=['DELETE'])
@require_auth
def delete_shot(shot_id):
    db = get_user_db()

    rows_affected = db.execute_update('DELETE FROM shot_lists WHERE id = %s', (shot_id,))

    if rows_affected > 0:
        return jsonify({'message': 'Shot deleted successfully'}), 200
    else:
        return jsonify({'error': 'Shot not found'}), 404

# Cast management routes
@app.route('/cast', methods=['GET'])
@require_auth
def get_cast():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400

    db = get_user_db()
    cast_members = db.execute_query(
        'SELECT * FROM cast WHERE project_id = %s ORDER BY name',
        (project_id,)
    )

    cast_list = []
    for member in cast_members:
        cast_list.append({
            'id': member[0],
            'project_id': member[1],
            'name': member[2],
            'character_name': member[3],
            'email': member[4],
            'phone': member[5],
            'notes': member[6],
            'created_at': member[7]
        })

    return jsonify({'cast': cast_list}), 200

@app.route('/cast', methods=['POST'])
@require_auth
def create_cast_member():
    data = request.get_json()

    if not data or not all(k in data for k in ('project_id', 'name')):
        return jsonify({'error': 'project_id and name are required'}), 400

    db = get_user_db()

    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = %s', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    cast_id = db.execute_insert('''
        INSERT INTO cast (project_id, name, character_name, email, phone, notes)
        VALUES (%s, %s, %s, %s, %s, %s)
    ''', (
        data['project_id'],
        data['name'],
        data.get('character_name', ''),
        data.get('email', ''),
        data.get('phone', ''),
        data.get('notes', '')
    ))

    return jsonify({
        'message': 'Cast member added successfully',
        'cast_id': cast_id
    }), 201

# Crew management routes
@app.route('/crew', methods=['GET'])
@require_auth
def get_crew():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400

    db = get_user_db()
    crew_members = db.execute_query(
        'SELECT * FROM crew WHERE project_id = %s ORDER BY department, role, name',
        (project_id,)
    )

    crew_list = []
    for member in crew_members:
        crew_list.append({
            'id': member[0],
            'project_id': member[1],
            'name': member[2],
            'role': member[3],
            'email': member[4],
            'phone': member[5],
            'department': member[6],
            'notes': member[7],
            'created_at': member[8]
        })

    return jsonify({'crew': crew_list}), 200

@app.route('/crew', methods=['POST'])
@require_auth
def create_crew_member():
    data = request.get_json()

    if not data or not all(k in data for k in ('project_id', 'name', 'role')):
        return jsonify({'error': 'project_id, name, and role are required'}), 400

    db = get_user_db()

    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = %s', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    crew_id = db.execute_insert('''
        INSERT INTO crew (project_id, name, role, email, phone, department, notes)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
    ''', (
        data['project_id'],
        data['name'],
        data['role'],
        data.get('email', ''),
        data.get('phone', ''),
        data.get('department', ''),
        data.get('notes', '')
    ))

    return jsonify({
        'message': 'Crew member added successfully',
        'crew_id': crew_id
    }), 201

# Script elements management routes
@app.route('/scripts/<int:script_id>/elements', methods=['GET'])
@require_auth
def get_script_elements(script_id):
    db = get_user_db()

    # Verify script exists and belongs to user
    script = db.execute_query('SELECT s.id FROM scripts s JOIN projects p ON s.project_id = p.id WHERE s.id = %s AND p.user_id = %s', (script_id, request.user['user_id']))
    if not script:
        return jsonify({'error': 'Script not found'}), 404

    elements = db.execute_query('''
        SELECT id, element_type, order_index, content, character_name, character_id, metadata
        FROM script_elements
        WHERE script_id = %s
        ORDER BY order_index
    ''', (script_id,))

    element_list = []
    for element in elements:
        element_data = {
            'id': element[0],
            'element_type': element[1],
            'order_index': element[2],
            'content': element[3],
            'character_name': element[4],
            'character_id': element[5],
            'metadata': json.loads(element[6]) if element[6] else {}
        }
        element_list.append(element_data)

    return jsonify({'elements': element_list}), 200

@app.route('/scripts/<int:script_id>/elements', methods=['POST'])
@require_auth
def create_script_element(script_id):
    data = request.get_json()

    if not data or not all(k in data for k in ('element_type', 'content')):
        return jsonify({'error': 'element_type and content are required'}), 400

    db = get_user_db()

    # Verify script exists and belongs to user
    script = db.execute_query('SELECT s.id, p.id as project_id FROM scripts s JOIN projects p ON s.project_id = p.id WHERE s.id = %s AND p.user_id = %s', (script_id, request.user['user_id']))
    if not script:
        return jsonify({'error': 'Script not found'}), 404

    project_id = script[0][1]

    # Get next order index
    max_order = db.execute_query('SELECT MAX(order_index) FROM script_elements WHERE script_id = %s', (script_id,))
    next_order = (max_order[0][0] or 0) + 1

    # Handle character creation/linking
    character_id = None
    character_name = data.get('character_name')

    if data['element_type'] == 'character' and character_name:
        # Check if character exists in project
        existing_char = db.execute_query('SELECT id FROM cast WHERE project_id = %s AND (name = %s OR character_name = %s)', (project_id, character_name, character_name))
        if existing_char:
            character_id = existing_char[0][0]
        else:
            # Auto-create character in cast
            try:
                character_id = db.execute_insert('''
                    INSERT INTO cast (project_id, name, character_name, notes)
                    VALUES (%s, %s, %s, %s)
                ''', (project_id, character_name, character_name, f'Auto-created from script element'))
            except Exception as e:
                print(f"Error creating character: {e}")
                # Continue without character_id if creation fails

    # Handle shot creation
    if data['element_type'] == 'shot':
        metadata = data.get('metadata', {})
        scene_number = metadata.get('scene_number', '1')
        shot_number = metadata.get('shot_number', '1')

        # Auto-create shot in shot list if it doesn't exist
        existing_shot = db.execute_query('''
            SELECT id FROM shot_lists sl JOIN scripts s ON sl.script_id = s.id
            WHERE s.id = %s AND sl.scene_number = %s AND sl.shot_number = %s
        ''', (script_id, scene_number, shot_number))

        if not existing_shot:
            try:
                db.execute_insert('''
                    INSERT INTO shot_lists (script_id, scene_number, shot_number, description, shot_type, camera_angle, movement)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                ''', (
                    script_id,
                    scene_number,
                    shot_number,
                    data['content'],
                    metadata.get('shot_type', ''),
                    metadata.get('camera_angle', ''),
                    metadata.get('movement', '')
                ))
            except Exception as e:
                print(f"Error creating shot: {e}")
                # Continue without creating shot if it fails

    # Create script element
    element_id = db.execute_insert('''
        INSERT INTO script_elements (script_id, element_type, order_index, content, character_name, character_id, metadata)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
    ''', (
        script_id,
        data['element_type'],
        data.get('order_index', next_order),
        data['content'],
        character_name,
        character_id,
        json.dumps(data.get('metadata', {}))
    ))

    return jsonify({
        'message': 'Script element created successfully',
        'element_id': element_id
    }), 201

@app.route('/scripts/<int:script_id>/elements/<int:element_id>', methods=['PUT'])
@require_auth
def update_script_element(script_id, element_id):
    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    db = get_user_db()

    # Verify element exists and belongs to user's script
    element = db.execute_query('''
        SELECT se.id FROM script_elements se
        JOIN scripts s ON se.script_id = s.id
        JOIN projects p ON s.project_id = p.id
        WHERE se.id = %s AND s.id = %s AND p.user_id = %s
    ''', (element_id, script_id, request.user['user_id']))

    if not element:
        return jsonify({'error': 'Script element not found'}), 404

    # Update element
    update_fields = []
    params = []

    for field in ['element_type', 'content', 'character_name', 'order_index']:
        if field in data:
            update_fields.append(f'{field} = %s')
            params.append(data[field])

    if 'metadata' in data:
        update_fields.append('metadata = %s')
        params.append(json.dumps(data['metadata']))

    if update_fields:
        params.append(element_id)
        query = f'UPDATE script_elements SET {", ".join(update_fields)} WHERE id = %s'
        db.execute_update(query, params)

    return jsonify({'message': 'Script element updated successfully'}), 200

@app.route('/scripts/<int:script_id>/elements/<int:element_id>', methods=['DELETE'])
@require_auth
def delete_script_element(script_id, element_id):
    db = get_user_db()

    # Verify element exists and belongs to user's script
    element = db.execute_query('''
        SELECT se.id FROM script_elements se
        JOIN scripts s ON se.script_id = s.id
        JOIN projects p ON s.project_id = p.id
        WHERE se.id = %s AND s.id = %s AND p.user_id = %s
    ''', (element_id, script_id, request.user['user_id']))

    if not element:
        return jsonify({'error': 'Script element not found'}), 404

    rows_affected = db.execute_update('DELETE FROM script_elements WHERE id = %s', (element_id,))

    if rows_affected > 0:
        return jsonify({'message': 'Script element deleted successfully'}), 200
    else:
        return jsonify({'error': 'Script element not found'}), 404

# Storyboard panel routes
@app.route('/scripts/<int:script_id>/storyboard-panels', methods=['GET'])
@require_auth
def get_storyboard_panels(script_id):
    db = get_user_db()

    # Verify script belongs to user
    script = db.execute_query('''
        SELECT s.id FROM scripts s
        JOIN projects p ON s.project_id = p.id
        WHERE s.id = %s AND p.user_id = %s
    ''', (script_id, request.user['user_id']))

    if not script:
        return jsonify({'error': 'Script not found'}), 404

    panels = db.execute_query('''
        SELECT sb.*, se.content as shot_content
        FROM storyboards sb
        LEFT JOIN script_elements se ON sb.scene_number = se.metadata->>'$.scene_number'
            AND se.element_type = 'shot'
            AND se.script_id = %s
        JOIN projects p ON sb.project_id = p.id
        WHERE p.user_id = %s
        ORDER BY sb.scene_number, sb.panel_number
    ''', (script_id, request.user['user_id']))

    panel_list = []
    for panel in panels:
        panel_list.append({
            'id': panel[0],
            'project_id': panel[1],
            'scene_number': panel[2],
            'panel_number': panel[3],
            'description': panel[4],
            'image_path': panel[5],
            'dialogue': panel[6],
            'camera_notes': panel[7],
            'created_at': panel[8],
            'shot_content': panel[9] if len(panel) > 9 else None
        })

    return jsonify({'panels': panel_list}), 200

@app.route('/scripts/<int:script_id>/storyboard-panels', methods=['POST'])
@require_auth
def create_storyboard_panel(script_id):
    data = request.get_json()

    if not data or not all(k in data for k in ('scene_number', 'panel_number')):
        return jsonify({'error': 'scene_number and panel_number are required'}), 400

    db = get_user_db()

    # Get project_id from script
    script = db.execute_query('''
        SELECT s.project_id FROM scripts s
        JOIN projects p ON s.project_id = p.id
        WHERE s.id = %s AND p.user_id = %s
    ''', (script_id, request.user['user_id']))

    if not script:
        return jsonify({'error': 'Script not found'}), 404

    project_id = script[0][0]

    panel_id = db.execute_insert('''
        INSERT INTO storyboards (project_id, scene_number, panel_number, description, dialogue, camera_notes)
        VALUES (%s, %s, %s, %s, %s, %s)
    ''', (
        project_id,
        data['scene_number'],
        data['panel_number'],
        data.get('description', ''),
        data.get('dialogue', ''),
        data.get('camera_notes', '')
    ))

    return jsonify({
        'message': 'Storyboard panel created successfully',
        'panel_id': panel_id
    }), 201

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
