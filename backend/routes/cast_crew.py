from flask import Blueprint, request, jsonify

cast_crew_bp = Blueprint('cast_crew', __name__)

def get_user_db():
    """Get database manager for current user - imported from main app"""
    from app import get_user_db as _get_user_db
    return _get_user_db()

# Cast management routes
@cast_crew_bp.route('/api/cast', methods=['GET'])
def get_cast():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400
    
    db = get_user_db()
    cast_members = db.execute_query(
        'SELECT * FROM cast WHERE project_id = ? ORDER BY name',
        (project_id,)
    )
    
    cast_list = []
    for member in cast_members:
        cast_list.append({
            'id': member[0],
            'project_id': member[1],
            'name': member[2],
            'character_name': member[3],
            'email': member[4],
            'phone': member[5],
            'notes': member[6],
            'created_at': member[7]
        })
    
    return jsonify({'cast': cast_list}), 200

@cast_crew_bp.route('/api/cast', methods=['POST'])
def create_cast_member():
    data = request.get_json()
    
    if not data or not all(k in data for k in ('project_id', 'name')):
        return jsonify({'error': 'project_id and name are required'}), 400
    
    db = get_user_db()
    
    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = ?', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    cast_id = db.execute_insert('''
        INSERT INTO cast (project_id, name, character_name, email, phone, notes)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        data['project_id'],
        data['name'],
        data.get('character_name', ''),
        data.get('email', ''),
        data.get('phone', ''),
        data.get('notes', '')
    ))
    
    return jsonify({
        'message': 'Cast member added successfully',
        'cast_id': cast_id
    }), 201

@cast_crew_bp.route('/api/cast/<int:cast_id>', methods=['PUT'])
def update_cast_member(cast_id):
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    db = get_user_db()
    
    # Check if cast member exists
    cast_member = db.execute_query('SELECT id FROM cast WHERE id = ?', (cast_id,))
    if not cast_member:
        return jsonify({'error': 'Cast member not found'}), 404
    
    # Update cast member
    update_fields = []
    params = []
    
    fields = ['name', 'character_name', 'email', 'phone', 'notes']
    
    for field in fields:
        if field in data:
            update_fields.append(f'{field} = ?')
            params.append(data[field])
    
    if update_fields:
        params.append(cast_id)
        query = f'UPDATE cast SET {", ".join(update_fields)} WHERE id = ?'
        db.execute_update(query, params)
    
    return jsonify({'message': 'Cast member updated successfully'}), 200

@cast_crew_bp.route('/api/cast/<int:cast_id>', methods=['DELETE'])
def delete_cast_member(cast_id):
    db = get_user_db()
    
    rows_affected = db.execute_update('DELETE FROM cast WHERE id = ?', (cast_id,))
    
    if rows_affected > 0:
        return jsonify({'message': 'Cast member deleted successfully'}), 200
    else:
        return jsonify({'error': 'Cast member not found'}), 404

# Crew management routes
@cast_crew_bp.route('/api/crew', methods=['GET'])
def get_crew():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400
    
    db = get_user_db()
    crew_members = db.execute_query(
        'SELECT * FROM crew WHERE project_id = ? ORDER BY department, role, name',
        (project_id,)
    )
    
    crew_list = []
    for member in crew_members:
        crew_list.append({
            'id': member[0],
            'project_id': member[1],
            'name': member[2],
            'role': member[3],
            'email': member[4],
            'phone': member[5],
            'department': member[6],
            'notes': member[7],
            'created_at': member[8]
        })
    
    return jsonify({'crew': crew_list}), 200

@cast_crew_bp.route('/api/crew', methods=['POST'])
def create_crew_member():
    data = request.get_json()
    
    if not data or not all(k in data for k in ('project_id', 'name', 'role')):
        return jsonify({'error': 'project_id, name, and role are required'}), 400
    
    db = get_user_db()
    
    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = ?', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    crew_id = db.execute_insert('''
        INSERT INTO crew (project_id, name, role, email, phone, department, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['project_id'],
        data['name'],
        data['role'],
        data.get('email', ''),
        data.get('phone', ''),
        data.get('department', ''),
        data.get('notes', '')
    ))
    
    return jsonify({
        'message': 'Crew member added successfully',
        'crew_id': crew_id
    }), 201

@cast_crew_bp.route('/api/crew/<int:crew_id>', methods=['PUT'])
def update_crew_member(crew_id):
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    db = get_user_db()
    
    # Check if crew member exists
    crew_member = db.execute_query('SELECT id FROM crew WHERE id = ?', (crew_id,))
    if not crew_member:
        return jsonify({'error': 'Crew member not found'}), 404
    
    # Update crew member
    update_fields = []
    params = []
    
    fields = ['name', 'role', 'email', 'phone', 'department', 'notes']
    
    for field in fields:
        if field in data:
            update_fields.append(f'{field} = ?')
            params.append(data[field])
    
    if update_fields:
        params.append(crew_id)
        query = f'UPDATE crew SET {", ".join(update_fields)} WHERE id = ?'
        db.execute_update(query, params)
    
    return jsonify({'message': 'Crew member updated successfully'}), 200

@cast_crew_bp.route('/api/crew/<int:crew_id>', methods=['DELETE'])
def delete_crew_member(crew_id):
    db = get_user_db()
    
    rows_affected = db.execute_update('DELETE FROM crew WHERE id = ?', (crew_id,))
    
    if rows_affected > 0:
        return jsonify({'message': 'Crew member deleted successfully'}), 200
    else:
        return jsonify({'error': 'Crew member not found'}), 404
