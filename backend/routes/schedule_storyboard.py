from flask import Blueprint, request, jsonify
import os
from werkzeug.utils import secure_filename

schedule_storyboard_bp = Blueprint('schedule_storyboard', __name__)

def get_user_db():
    """Get database manager for current user - imported from main app"""
    from app import get_user_db as _get_user_db
    return _get_user_db()

# Shooting schedule routes
@schedule_storyboard_bp.route('/api/schedule', methods=['GET'])
def get_schedule():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400
    
    db = get_user_db()
    schedule_items = db.execute_query(
        'SELECT * FROM shooting_schedule WHERE project_id = ? ORDER BY shoot_date, start_time',
        (project_id,)
    )
    
    schedule_list = []
    for item in schedule_items:
        schedule_list.append({
            'id': item[0],
            'project_id': item[1],
            'scene_number': item[2],
            'location': item[3],
            'shoot_date': item[4],
            'start_time': item[5],
            'end_time': item[6],
            'cast_required': item[7],
            'crew_required': item[8],
            'equipment_needed': item[9],
            'notes': item[10],
            'status': item[11],
            'created_at': item[12]
        })
    
    return jsonify({'schedule': schedule_list}), 200

@schedule_storyboard_bp.route('/api/schedule', methods=['POST'])
def create_schedule_item():
    data = request.get_json()
    
    if not data or not all(k in data for k in ('project_id', 'scene_number', 'shoot_date')):
        return jsonify({'error': 'project_id, scene_number, and shoot_date are required'}), 400
    
    db = get_user_db()
    
    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = ?', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    schedule_id = db.execute_insert('''
        INSERT INTO shooting_schedule (project_id, scene_number, location, shoot_date, 
                                     start_time, end_time, cast_required, crew_required, 
                                     equipment_needed, notes, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['project_id'],
        data['scene_number'],
        data.get('location', ''),
        data['shoot_date'],
        data.get('start_time', ''),
        data.get('end_time', ''),
        data.get('cast_required', ''),
        data.get('crew_required', ''),
        data.get('equipment_needed', ''),
        data.get('notes', ''),
        data.get('status', 'planned')
    ))
    
    return jsonify({
        'message': 'Schedule item created successfully',
        'schedule_id': schedule_id
    }), 201

@schedule_storyboard_bp.route('/api/schedule/<int:schedule_id>', methods=['PUT'])
def update_schedule_item(schedule_id):
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    db = get_user_db()
    
    # Check if schedule item exists
    schedule_item = db.execute_query('SELECT id FROM shooting_schedule WHERE id = ?', (schedule_id,))
    if not schedule_item:
        return jsonify({'error': 'Schedule item not found'}), 404
    
    # Update schedule item
    update_fields = []
    params = []
    
    fields = ['scene_number', 'location', 'shoot_date', 'start_time', 'end_time',
              'cast_required', 'crew_required', 'equipment_needed', 'notes', 'status']
    
    for field in fields:
        if field in data:
            update_fields.append(f'{field} = ?')
            params.append(data[field])
    
    if update_fields:
        params.append(schedule_id)
        query = f'UPDATE shooting_schedule SET {", ".join(update_fields)} WHERE id = ?'
        db.execute_update(query, params)
    
    return jsonify({'message': 'Schedule item updated successfully'}), 200

@schedule_storyboard_bp.route('/api/schedule/<int:schedule_id>', methods=['DELETE'])
def delete_schedule_item(schedule_id):
    db = get_user_db()
    
    rows_affected = db.execute_update('DELETE FROM shooting_schedule WHERE id = ?', (schedule_id,))
    
    if rows_affected > 0:
        return jsonify({'message': 'Schedule item deleted successfully'}), 200
    else:
        return jsonify({'error': 'Schedule item not found'}), 404

# Storyboard routes
@schedule_storyboard_bp.route('/api/storyboard', methods=['GET'])
def get_storyboard():
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify({'error': 'project_id parameter is required'}), 400
    
    db = get_user_db()
    storyboard_panels = db.execute_query(
        'SELECT * FROM storyboards WHERE project_id = ? ORDER BY scene_number, panel_number',
        (project_id,)
    )
    
    storyboard_list = []
    for panel in storyboard_panels:
        storyboard_list.append({
            'id': panel[0],
            'project_id': panel[1],
            'scene_number': panel[2],
            'panel_number': panel[3],
            'description': panel[4],
            'image_path': panel[5],
            'dialogue': panel[6],
            'camera_notes': panel[7],
            'created_at': panel[8]
        })
    
    return jsonify({'storyboard': storyboard_list}), 200

@schedule_storyboard_bp.route('/api/storyboard', methods=['POST'])
def create_storyboard_panel():
    data = request.get_json()
    
    if not data or not all(k in data for k in ('project_id', 'scene_number', 'panel_number')):
        return jsonify({'error': 'project_id, scene_number, and panel_number are required'}), 400
    
    db = get_user_db()
    
    # Verify project exists
    project = db.execute_query('SELECT id FROM projects WHERE id = ?', (data['project_id'],))
    if not project:
        return jsonify({'error': 'Project not found'}), 404
    
    panel_id = db.execute_insert('''
        INSERT INTO storyboards (project_id, scene_number, panel_number, description, 
                               image_path, dialogue, camera_notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        data['project_id'],
        data['scene_number'],
        data['panel_number'],
        data.get('description', ''),
        data.get('image_path', ''),
        data.get('dialogue', ''),
        data.get('camera_notes', '')
    ))
    
    return jsonify({
        'message': 'Storyboard panel created successfully',
        'panel_id': panel_id
    }), 201

@schedule_storyboard_bp.route('/api/storyboard/<int:panel_id>', methods=['PUT'])
def update_storyboard_panel(panel_id):
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    db = get_user_db()
    
    # Check if panel exists
    panel = db.execute_query('SELECT id FROM storyboards WHERE id = ?', (panel_id,))
    if not panel:
        return jsonify({'error': 'Storyboard panel not found'}), 404
    
    # Update panel
    update_fields = []
    params = []
    
    fields = ['scene_number', 'panel_number', 'description', 'image_path', 'dialogue', 'camera_notes']
    
    for field in fields:
        if field in data:
            update_fields.append(f'{field} = ?')
            params.append(data[field])
    
    if update_fields:
        params.append(panel_id)
        query = f'UPDATE storyboards SET {", ".join(update_fields)} WHERE id = ?'
        db.execute_update(query, params)
    
    return jsonify({'message': 'Storyboard panel updated successfully'}), 200

@schedule_storyboard_bp.route('/api/storyboard/<int:panel_id>', methods=['DELETE'])
def delete_storyboard_panel(panel_id):
    db = get_user_db()
    
    rows_affected = db.execute_update('DELETE FROM storyboards WHERE id = ?', (panel_id,))
    
    if rows_affected > 0:
        return jsonify({'message': 'Storyboard panel deleted successfully'}), 200
    else:
        return jsonify({'error': 'Storyboard panel not found'}), 404
