#!/usr/bin/env python3
"""
Basic API tests for the Scriptwriter application
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000/api"

def test_user_registration_and_login():
    """Test user registration and login flow"""
    print("Testing user registration and login...")
    
    # Test registration
    register_data = {
        "username": "testuser2",
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
    assert response.status_code == 201, f"Registration failed: {response.text}"
    print("✓ User registration successful")
    
    # Test login
    login_data = {
        "username": "testuser2",
        "password": "testpass123"
    }
    
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=login_data)
    assert response.status_code == 200, f"Login failed: {response.text}"
    print("✓ User login successful")
    
    return session

def test_project_management(session):
    """Test project CRUD operations"""
    print("Testing project management...")
    
    # Create project
    project_data = {
        "name": "Test Project",
        "description": "A test project for API testing"
    }
    
    response = session.post(f"{BASE_URL}/projects", json=project_data)
    assert response.status_code == 201, f"Project creation failed: {response.text}"
    project_id = response.json()["project_id"]
    print("✓ Project creation successful")
    
    # Get projects
    response = session.get(f"{BASE_URL}/projects")
    assert response.status_code == 200, f"Get projects failed: {response.text}"
    projects = response.json()["projects"]
    assert len(projects) > 0, "No projects returned"
    print("✓ Get projects successful")
    
    # Update project
    update_data = {
        "name": "Updated Test Project",
        "description": "Updated description"
    }
    
    response = session.put(f"{BASE_URL}/projects/{project_id}", json=update_data)
    assert response.status_code == 200, f"Project update failed: {response.text}"
    print("✓ Project update successful")
    
    # Get specific project
    response = session.get(f"{BASE_URL}/projects/{project_id}")
    assert response.status_code == 200, f"Get project failed: {response.text}"
    project = response.json()["project"]
    assert project["name"] == "Updated Test Project", "Project name not updated"
    print("✓ Get specific project successful")
    
    return project_id

def test_script_management(session, project_id):
    """Test script CRUD operations"""
    print("Testing script management...")
    
    # Create script
    script_data = {
        "project_id": project_id,
        "title": "Test Script",
        "content": "FADE IN:\n\nINT. TEST LOCATION - DAY\n\nThis is a test script.\n\nFADE OUT.",
        "format_type": "screenplay"
    }
    
    response = session.post(f"{BASE_URL}/scripts", json=script_data)
    assert response.status_code == 201, f"Script creation failed: {response.text}"
    script_id = response.json()["script_id"]
    print("✓ Script creation successful")
    
    # Get scripts
    response = session.get(f"{BASE_URL}/scripts?project_id={project_id}")
    assert response.status_code == 200, f"Get scripts failed: {response.text}"
    scripts = response.json()["scripts"]
    assert len(scripts) > 0, "No scripts returned"
    print("✓ Get scripts successful")
    
    # Update script
    update_data = {
        "title": "Updated Test Script",
        "content": "FADE IN:\n\nINT. UPDATED LOCATION - DAY\n\nThis is an updated test script.\n\nFADE OUT."
    }
    
    response = session.put(f"{BASE_URL}/scripts/{script_id}", json=update_data)
    assert response.status_code == 200, f"Script update failed: {response.text}"
    print("✓ Script update successful")
    
    return script_id

def test_authentication_required():
    """Test that authentication is required for protected endpoints"""
    print("Testing authentication requirements...")
    
    # Try to access projects without authentication
    response = requests.get(f"{BASE_URL}/projects")
    assert response.status_code == 401, "Projects endpoint should require authentication"
    print("✓ Authentication required for protected endpoints")

def run_tests():
    """Run all tests"""
    print("Starting API tests...\n")
    
    try:
        # Test authentication
        test_authentication_required()
        
        # Test user registration and login
        session = test_user_registration_and_login()
        
        # Test project management
        project_id = test_project_management(session)
        
        # Test script management
        script_id = test_script_management(session, project_id)
        
        print("\n✅ All tests passed!")
        return True
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
