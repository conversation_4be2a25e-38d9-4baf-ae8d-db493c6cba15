#!/bin/bash

echo "🚀 Setting up MySQL for scriptwriter application..."

# Deploy MySQL first
echo "📦 Deploying MySQL..."
./deploy-mysql.sh

if [ $? -ne 0 ]; then
    echo "❌ MySQL deployment failed!"
    exit 1
fi

# Install Python dependencies
echo "📥 Installing Python dependencies..."
cd backend
source ../venv/bin/activate
pip install mysql-connector-python==8.2.0

if [ $? -eq 0 ]; then
    echo "✅ MySQL setup complete!"
    echo ""
    echo "🔧 Next steps:"
    echo "1. Start your Flask app: cd backend && python app.py"
    echo "2. Your app will now use MySQL instead of SQLite"
    echo ""
    echo "📋 Database Connection:"
    echo "   Host: localhost:3306"
    echo "   Database: scriptwriter"
    echo "   User: scriptwriter_api"
else
    echo "❌ Failed to install MySQL connector!"
    exit 1
fi
