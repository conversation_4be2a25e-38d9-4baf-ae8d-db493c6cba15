# More Than Scripts - Web-based Scriptwriting and Production Management

A web application similar to Celtx that allows users to create and manage film/video production projects including scripts, shot lists, cast/crew management, shooting schedules, and storyboards.

## Features

- **User Registration & Authentication**: Self-registration with individual user databases
- **Project Management**: Create and manage multiple production projects
- **Script Writing**: Professional script formatting and editing
- **Shot List Generation**: Automatic generation from scripts with manual editing
- **Cast & Crew Management**: Contact database and role assignments
- **Shooting Schedule**: Calendar-based scheduling with resource allocation
- **Storyboard Creation**: Text and image-based storyboarding

## Technology Stack

- **Frontend**: Vanilla HTML, CSS, JavaScript
- **Backend**: Python Flask API
- **Database**: SQLite (individual database per user)
- **Authentication**: Session-based authentication

## Project Structure

```
more-than-scripts/
├── backend/                 # Flask API backend
│   ├── app.py              # Main Flask application
│   ├── models/             # Database models
│   ├── routes/             # API route handlers
│   ├── utils/              # Utility functions
│   └── requirements.txt    # Python dependencies
├── frontend/               # Frontend web interface
│   ├── index.html          # Main application page
│   ├── login.html          # Login/registration page
│   ├── css/                # Stylesheets
│   ├── js/                 # JavaScript files
│   └── assets/             # Images and other assets
├── databases/              # User database files
└── docs/                   # Documentation
```

## Quick Setup

Run the automated setup script:
```bash
./setup.sh
```

This will:
- Create a Python virtual environment
- Install all dependencies
- Run tests to verify the setup
- Provide instructions to start the application

## Manual Setup

If you prefer to set up manually:

1. Create and activate a virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

2. Install Python dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. Start the Flask development server:
   ```bash
   python app.py
   ```

4. Open your browser to `http://localhost:5000`

## Testing

Run the API tests to verify everything is working:
```bash
cd backend
source ../venv/bin/activate
python test_api.py
```

## API Endpoints

- `/api/auth/register` - User registration
- `/api/auth/login` - User login
- `/api/auth/logout` - User logout
- `/api/projects` - Project management
- `/api/scripts` - Script management
- `/api/shotlists` - Shot list management
- `/api/cast` - Cast management
- `/api/crew` - Crew management
- `/api/schedule` - Shooting schedule
- `/api/storyboard` - Storyboard management

## License

MIT License
