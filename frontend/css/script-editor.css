/* Script Editor Styles */

.script-info {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.script-info h2 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.script-meta {
    display: flex;
    gap: 2rem;
    color: #666;
    font-size: 0.9rem;
}

.script-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
}

.editor-container {
    display: flex;
    flex: 1;
    gap: 1rem;
    padding: 1rem;
    height: calc(100vh - 200px);
}

/* Script Document */
.script-document {
    flex: 4;
    background: white;
    border: 1px solid #ccc;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    padding: 1in;
    margin: 0 auto;
    width: 8.5in;
    min-height: 11in;
    max-height: calc(100vh - 250px);
    overflow-y: auto;
    font-family: 'Courier New', 'Courier', monospace;
    font-size: 12pt;
    line-height: 1.5;
    position: relative;
    outline: none;
    cursor: text;
}

.script-content {
    margin-bottom: 1rem;
}

.current-line {
    position: relative;
    min-height: 1.5em;
    margin-bottom: 1rem;
}

.line-content {
    display: inline;
}

.cursor {
    display: inline;
    animation: blink 1s infinite;
    color: #333;
}

.script-elements {
    margin-bottom: 0;
}

/* Current Element Being Typed */
.current-element {
    position: relative;
    min-height: 1.5em;
    margin-bottom: 1rem;
}

.element-cursor {
    display: inline;
    color: #333;
    animation: blink 1s infinite;
    font-weight: normal;
}

.element-content {
    display: inline;
}

.script-element {
    margin-bottom: 0.5rem;
    position: relative;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
    line-height: 1.8;
}

.script-element:hover {
    background-color: rgba(248, 249, 250, 0.8);
}

.script-element.active {
    background-color: rgba(227, 242, 253, 0.8);
    border-left: 3px solid #2196f3;
}

/* Screenplay Element Formatting */
.script-line, .current-line {
    margin-bottom: 0.5rem;
    min-height: 1.5em;
    position: relative;
}

/* Action - Full width paragraph */
.element-action, .current-line.element-action {
    text-align: left;
    margin: 1rem 0;
    width: 100%;
    line-height: 1.5;
}

/* Character - All caps, centered */
.element-character, .current-line.element-character {
    text-align: center;
    text-transform: uppercase;
    font-weight: bold;
    margin: 1.5rem 0 0.5rem 0;
}

/* Dialog - Narrow center aligned paragraph */
.element-dialog, .current-line.element-dialog {
    text-align: left;
    margin: 0.5rem auto;
    width: 3.5in;
    padding-left: 1in;
    line-height: 1.4;
}

/* Parenthetical - Italic, narrow, centered */
.element-parenthetical, .current-line.element-parenthetical {
    text-align: left;
    font-style: italic;
    margin: 0.25rem auto;
    width: 2in;
    padding-left: 1.5in;
}

/* Transition - All caps, right aligned, ends with : */
.element-transition, .current-line.element-transition {
    text-align: right;
    text-transform: uppercase;
    font-weight: bold;
    margin: 1.5rem 0;
}

/* Shot - All caps, left aligned, grey background full width */
.element-shot, .current-line.element-shot {
    text-align: left;
    text-transform: uppercase;
    font-weight: bold;
    background-color: #f0f0f0;
    padding: 0.5rem;
    margin: 1rem -1in;
    width: calc(100% + 2in);
}

/* Act - Centered, all caps */
.element-act, .current-line.element-act {
    text-align: center;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 14pt;
    margin: 3rem 0;
    text-decoration: underline;
}

/* Scene - All caps, left aligned */
.element-scene, .current-line.element-scene {
    text-align: left;
    text-transform: uppercase;
    font-weight: bold;
    margin: 2rem 0 1rem 0;
}

/* Blinking cursor animation */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Ensure no unwanted elements appear */
.hidden {
    display: none !important;
}

.modal {
    display: none !important;
}

/* Clean up any stray elements */
.timeline-container,
.storyboard-timeline,
.create-character-modal,
#storyboard-timeline-modal,
#create-character-modal {
    display: none !important;
}

/* Controls Panel */
.controls-panel {
    flex: 1;
    min-width: 280px;
    max-width: 350px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    height: fit-content;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

.element-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.element-type-select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

.character-selection, .shot-options {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.character-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 0.5rem 0;
    max-height: 200px;
    overflow-y: auto;
}

.character-item {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.character-item:hover {
    background-color: #f0f0f0;
}

.character-item.selected {
    background-color: #2196f3;
    color: white;
}

#shot-location, #shot-time {
    padding: 0.25rem;
    margin: 0.25rem 0;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.element-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.element-type-select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

.character-selection {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.character-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 0.5rem 0;
    max-height: 200px;
    overflow-y: auto;
}

.character-item {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.character-item:hover {
    background-color: #f0f0f0;
}

.character-item.selected {
    background-color: #2196f3;
    color: white;
}

.metadata-fields {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.metadata-field {
    margin-bottom: 1rem;
}

.metadata-field label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.metadata-field input,
.metadata-field textarea,
.metadata-field select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
}

.metadata-field textarea {
    resize: vertical;
    min-height: 60px;
}

/* Timeline Styles */
.timeline-container {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1rem;
}

.timeline-scene {
    margin-bottom: 2rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.timeline-scene-header {
    background: #f8f9fa;
    padding: 1rem;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
}

.timeline-shots {
    padding: 1rem;
}

.timeline-shot {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.timeline-shot:last-child {
    border-bottom: none;
}

.shot-info {
    flex: 1;
}

.shot-characters {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.storyboard-panel {
    width: 100px;
    height: 60px;
    border: 2px dashed #ccc;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.2s;
}

.storyboard-panel:hover {
    border-color: #2196f3;
}

.storyboard-panel.has-panel {
    border-style: solid;
    background: #f0f0f0;
}

/* Responsive */
@media (max-width: 1024px) {
    .editor-container {
        gap: 0.5rem;
    }

    .controls-panel {
        min-width: 250px;
        max-width: 300px;
    }

    .script-content-panel {
        padding: 0.5in;
    }
}

@media (max-width: 768px) {
    .editor-container {
        flex-direction: column;
        height: auto;
    }

    .controls-panel {
        width: 100%;
        min-width: auto;
        max-width: none;
        order: -1;
        max-height: 300px;
    }

    .script-content-panel {
        height: 60vh;
        min-height: 400px;
        padding: 0.5in;
    }
}
