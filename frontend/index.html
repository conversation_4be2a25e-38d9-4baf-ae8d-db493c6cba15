<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>More Than Scripts - Dashboard</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">More Than Scripts</h1>
                <nav class="nav">
                    <button class="nav-btn active" data-section="projects">Projects</button>
                    <button class="nav-btn" data-section="scripts">Scripts</button>
                    <button class="nav-btn" data-section="shotlists">Shot Lists</button>
                    <button class="nav-btn" data-section="cast">Cast</button>
                    <button class="nav-btn" data-section="crew">Crew</button>
                    <button class="nav-btn" data-section="schedule">Schedule</button>
                    <button class="nav-btn" data-section="storyboard">Storyboard</button>
                </nav>
                <div class="user-menu">
                    <span id="username"></span>
                    <button class="btn btn-secondary" onclick="logout()">Logout</button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Projects Section -->
            <section id="projects-section" class="content-section active">
                <div class="section-header">
                    <h2>Projects</h2>
                    <button class="btn btn-primary" onclick="showCreateProjectModal()">New Project</button>
                </div>
                <div id="projects-list" class="projects-grid">
                    <!-- Projects will be loaded here -->
                </div>
            </section>

            <!-- Scripts Section -->
            <section id="scripts-section" class="content-section">
                <div class="section-header">
                    <h2>Scripts</h2>
                    <select id="script-project-select" class="project-select">
                        <option value="">Select a project...</option>
                    </select>
                    <button class="btn btn-primary" onclick="showCreateScriptModal()">New Script</button>
                </div>
                <div id="scripts-list" class="items-list">
                    <!-- Scripts will be loaded here -->
                </div>
            </section>

            <!-- Shot Lists Section -->
            <section id="shotlists-section" class="content-section">
                <div class="section-header">
                    <h2>Shot Lists</h2>
                    <select id="shotlist-script-select" class="project-select">
                        <option value="">Select a script...</option>
                    </select>
                    <button class="btn btn-primary" onclick="showCreateShotModal()">New Shot</button>
                </div>
                <div id="shotlists-list" class="items-list">
                    <!-- Shot lists will be loaded here -->
                </div>
            </section>

            <!-- Cast Section -->
            <section id="cast-section" class="content-section">
                <div class="section-header">
                    <h2>Cast</h2>
                    <select id="cast-project-select" class="project-select">
                        <option value="">Select a project...</option>
                    </select>
                    <button class="btn btn-primary" onclick="showCreateCastModal()">Add Cast Member</button>
                </div>
                <div id="cast-list" class="items-list">
                    <!-- Cast will be loaded here -->
                </div>
            </section>

            <!-- Crew Section -->
            <section id="crew-section" class="content-section">
                <div class="section-header">
                    <h2>Crew</h2>
                    <select id="crew-project-select" class="project-select">
                        <option value="">Select a project...</option>
                    </select>
                    <button class="btn btn-primary" onclick="showCreateCrewModal()">Add Crew Member</button>
                </div>
                <div id="crew-list" class="items-list">
                    <!-- Crew will be loaded here -->
                </div>
            </section>

            <!-- Schedule Section -->
            <section id="schedule-section" class="content-section">
                <div class="section-header">
                    <h2>Shooting Schedule</h2>
                    <select id="schedule-project-select" class="project-select">
                        <option value="">Select a project...</option>
                    </select>
                    <button class="btn btn-primary" onclick="showCreateScheduleModal()">Add Schedule Item</button>
                </div>
                <div id="schedule-list" class="items-list">
                    <!-- Schedule will be loaded here -->
                </div>
            </section>

            <!-- Storyboard Section -->
            <section id="storyboard-section" class="content-section">
                <div class="section-header">
                    <h2>Storyboard</h2>
                    <select id="storyboard-project-select" class="project-select">
                        <option value="">Select a project...</option>
                    </select>
                    <button class="btn btn-primary" onclick="showCreateStoryboardModal()">Add Panel</button>
                </div>
                <div id="storyboard-list" class="storyboard-grid">
                    <!-- Storyboard will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Modals will be added here -->
    <div id="modal-container"></div>
    
    <script src="js/app.js"></script>
    <script src="js/api.js"></script>
    <script src="js/modals.js"></script>
</body>
</html>
