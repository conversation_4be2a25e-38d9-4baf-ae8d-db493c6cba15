// API helper functions
// Detect environment and set API base URL
const API_BASE = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')
    ? 'http://localhost:5000'
    : 'https://api.morethanscripts.com';

// Generic API call function
async function apiCall(endpoint, method = 'GET', data = null) {
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(`${API_BASE}${endpoint}`, options);
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'An error occurred');
        }
        
        return result;
    } catch (error) {
        throw error;
    }
}

// Project API functions
async function createProject(projectData) {
    return await apiCall('/projects', 'POST', projectData);
}

async function updateProject(projectId, projectData) {
    return await apiCall(`/projects/${projectId}`, 'PUT', projectData);
}

async function deleteProjectApi(projectId) {
    return await apiCall(`/projects/${projectId}`, 'DELETE');
}

// Script API functions
async function createScript(scriptData) {
    return await apiCall('/scripts', 'POST', scriptData);
}

async function updateScript(scriptId, scriptData) {
    return await apiCall(`/scripts/${scriptId}`, 'PUT', scriptData);
}

async function deleteScriptApi(scriptId) {
    return await apiCall(`/scripts/${scriptId}`, 'DELETE');
}

async function getScript(scriptId) {
    return await apiCall(`/scripts/${scriptId}`);
}

// Shot list API functions
async function createShot(shotData) {
    return await apiCall('/shotlists', 'POST', shotData);
}

async function updateShot(shotId, shotData) {
    return await apiCall(`/shotlists/${shotId}`, 'PUT', shotData);
}

async function deleteShotApi(shotId) {
    return await apiCall(`/shotlists/${shotId}`, 'DELETE');
}

// Cast API functions
async function createCastMember(castData) {
    return await apiCall('/cast', 'POST', castData);
}

async function updateCastMember(castId, castData) {
    return await apiCall(`/cast/${castId}`, 'PUT', castData);
}

async function deleteCastMemberApi(castId) {
    return await apiCall(`/cast/${castId}`, 'DELETE');
}

// Crew API functions
async function createCrewMember(crewData) {
    return await apiCall('/crew', 'POST', crewData);
}

async function updateCrewMember(crewId, crewData) {
    return await apiCall(`/crew/${crewId}`, 'PUT', crewData);
}

async function deleteCrewMemberApi(crewId) {
    return await apiCall(`/crew/${crewId}`, 'DELETE');
}

// Schedule API functions
async function createScheduleItem(scheduleData) {
    return await apiCall('/schedule', 'POST', scheduleData);
}

async function updateScheduleItem(scheduleId, scheduleData) {
    return await apiCall(`/schedule/${scheduleId}`, 'PUT', scheduleData);
}

async function deleteScheduleItemApi(scheduleId) {
    return await apiCall(`/schedule/${scheduleId}`, 'DELETE');
}

// Storyboard API functions
async function createStoryboardPanel(panelData) {
    return await apiCall('/storyboard', 'POST', panelData);
}

async function updateStoryboardPanel(panelId, panelData) {
    return await apiCall(`/storyboard/${panelId}`, 'PUT', panelData);
}

async function deleteStoryboardPanelApi(panelId) {
    return await apiCall(`/storyboard/${panelId}`, 'DELETE');
}
