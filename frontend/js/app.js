// Main application logic
let currentUser = null;
let currentProject = null;
let projects = [];

// Initialize app
document.addEventListener('DOMContentLoaded', async () => {
    await checkAuthentication();
    setupNavigation();
    await loadProjects();
});

// Check if user is authenticated
async function checkAuthentication() {
    try {
        const result = await apiCall('/auth/me');
        currentUser = result.user;
        document.getElementById('username').textContent = currentUser.username;
    } catch (error) {
        // Redirect to login if not authenticated
        window.location.href = '/login.html';
    }
}

// Setup navigation
function setupNavigation() {
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const section = btn.dataset.section;
            showSection(section);
            
            // Update active nav button
            navButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });
}

// Show specific section
function showSection(sectionName) {
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));
    
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
        
        // Load data for the section
        switch (sectionName) {
            case 'projects':
                loadProjects();
                break;
            case 'scripts':
                loadScripts();
                break;
            case 'shotlists':
                loadShotLists();
                break;
            case 'cast':
                loadCast();
                break;
            case 'crew':
                loadCrew();
                break;
            case 'schedule':
                loadSchedule();
                break;
            case 'storyboard':
                loadStoryboard();
                break;
        }
    }
}

// Load projects
async function loadProjects() {
    try {
        const result = await apiCall('/projects');
        projects = result.projects;
        displayProjects();
        updateProjectSelects();
    } catch (error) {
        console.error('Error loading projects:', error);
    }
}

// Display projects
function displayProjects() {
    const container = document.getElementById('projects-list');
    
    if (projects.length === 0) {
        container.innerHTML = '<p>No projects yet. Create your first project!</p>';
        return;
    }
    
    container.innerHTML = projects.map(project => `
        <div class="project-card">
            <h3>${escapeHtml(project.name)}</h3>
            <p>${escapeHtml(project.description || 'No description')}</p>
            <p class="project-date">Created: ${formatDate(project.created_at)}</p>
            <div class="project-actions">
                <button class="btn btn-primary" onclick="selectProject(${project.id})">Select</button>
                <button class="btn btn-secondary" onclick="editProject(${project.id})">Edit</button>
                <button class="btn btn-danger" onclick="deleteProject(${project.id})">Delete</button>
            </div>
        </div>
    `).join('');
}

// Update project select dropdowns
function updateProjectSelects() {
    const selects = document.querySelectorAll('.project-select');
    const options = projects.map(project => 
        `<option value="${project.id}">${escapeHtml(project.name)}</option>`
    ).join('');
    
    selects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">Select a project...</option>' + options;
        if (currentValue) {
            select.value = currentValue;
        }
    });
}

// Select project
function selectProject(projectId) {
    currentProject = projects.find(p => p.id === projectId);
    if (currentProject) {
        // Update all project selects
        document.querySelectorAll('.project-select').forEach(select => {
            select.value = projectId;
        });
        
        // Show success message
        showNotification(`Selected project: ${currentProject.name}`, 'success');
    }
}

// Load scripts
async function loadScripts() {
    const projectId = document.getElementById('script-project-select').value;
    if (!projectId) {
        document.getElementById('scripts-list').innerHTML = '<p>Please select a project first.</p>';
        return;
    }
    
    try {
        const result = await apiCall(`/scripts?project_id=${projectId}`);
        displayScripts(result.scripts);
    } catch (error) {
        console.error('Error loading scripts:', error);
        document.getElementById('scripts-list').innerHTML = '<p>Error loading scripts.</p>';
    }
}

// Display scripts
function displayScripts(scripts) {
    const container = document.getElementById('scripts-list');
    
    if (scripts.length === 0) {
        container.innerHTML = '<p>No scripts yet. Create your first script!</p>';
        return;
    }
    
    container.innerHTML = scripts.map(script => `
        <div class="item">
            <div class="item-info">
                <h4>${escapeHtml(script.title)}</h4>
                <p>Format: ${escapeHtml(script.format_type)} | Updated: ${formatDate(script.updated_at)}</p>
            </div>
            <div class="item-actions">
                <button class="btn btn-primary" onclick="editScript(${script.id})">Edit</button>
                <button class="btn btn-danger" onclick="deleteScript(${script.id})">Delete</button>
            </div>
        </div>
    `).join('');
}

// Logout function
async function logout() {
    try {
        await apiCall('/auth/logout', 'POST');
        window.location.href = '/login.html';
    } catch (error) {
        console.error('Logout error:', error);
        // Force redirect even if logout fails
        window.location.href = '/login.html';
    }
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem',
        borderRadius: '5px',
        color: 'white',
        backgroundColor: type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8',
        zIndex: '1000',
        boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
    });
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Project select change handlers
document.getElementById('script-project-select').addEventListener('change', loadScripts);
document.getElementById('cast-project-select').addEventListener('change', loadCast);
document.getElementById('crew-project-select').addEventListener('change', loadCrew);
document.getElementById('schedule-project-select').addEventListener('change', loadSchedule);
document.getElementById('storyboard-project-select').addEventListener('change', loadStoryboard);

// Load cast
async function loadCast() {
    const projectId = document.getElementById('cast-project-select').value;
    if (!projectId) {
        document.getElementById('cast-list').innerHTML = '<p>Please select a project first.</p>';
        return;
    }

    try {
        const result = await apiCall(`/cast?project_id=${projectId}`);
        displayCast(result.cast);
    } catch (error) {
        console.error('Error loading cast:', error);
        document.getElementById('cast-list').innerHTML = '<p>Error loading cast.</p>';
    }
}

// Display cast
function displayCast(cast) {
    const container = document.getElementById('cast-list');

    if (cast.length === 0) {
        container.innerHTML = '<p>No cast members yet. Add your first cast member!</p>';
        return;
    }

    container.innerHTML = cast.map(member => `
        <div class="item">
            <div class="item-info">
                <h4>${escapeHtml(member.name)}</h4>
                <p>Character: ${escapeHtml(member.character_name || 'Not specified')} |
                   Email: ${escapeHtml(member.email || 'Not provided')}</p>
                ${member.phone ? `<p>Phone: ${escapeHtml(member.phone)}</p>` : ''}
                ${member.notes ? `<p>Notes: ${escapeHtml(member.notes)}</p>` : ''}
            </div>
            <div class="item-actions">
                <button class="btn btn-primary" onclick="editCastMember(${member.id})">Edit</button>
                <button class="btn btn-danger" onclick="deleteCastMember(${member.id})">Delete</button>
            </div>
        </div>
    `).join('');
}

// Load crew
async function loadCrew() {
    const projectId = document.getElementById('crew-project-select').value;
    if (!projectId) {
        document.getElementById('crew-list').innerHTML = '<p>Please select a project first.</p>';
        return;
    }

    try {
        const result = await apiCall(`/crew?project_id=${projectId}`);
        displayCrew(result.crew);
    } catch (error) {
        console.error('Error loading crew:', error);
        document.getElementById('crew-list').innerHTML = '<p>Error loading crew.</p>';
    }
}

// Display crew
function displayCrew(crew) {
    const container = document.getElementById('crew-list');

    if (crew.length === 0) {
        container.innerHTML = '<p>No crew members yet. Add your first crew member!</p>';
        return;
    }

    container.innerHTML = crew.map(member => `
        <div class="item">
            <div class="item-info">
                <h4>${escapeHtml(member.name)}</h4>
                <p>Role: ${escapeHtml(member.role)} |
                   Department: ${escapeHtml(member.department || 'Not specified')}</p>
                <p>Email: ${escapeHtml(member.email || 'Not provided')}</p>
                ${member.phone ? `<p>Phone: ${escapeHtml(member.phone)}</p>` : ''}
                ${member.notes ? `<p>Notes: ${escapeHtml(member.notes)}</p>` : ''}
            </div>
            <div class="item-actions">
                <button class="btn btn-primary" onclick="editCrewMember(${member.id})">Edit</button>
                <button class="btn btn-danger" onclick="deleteCrewMember(${member.id})">Delete</button>
            </div>
        </div>
    `).join('');
}

// Placeholder functions for other sections
async function loadShotLists() {
    // Implementation will be added
    document.getElementById('shotlists-list').innerHTML = '<p>Shot lists feature coming soon...</p>';
}

async function loadSchedule() {
    // Implementation will be added
    document.getElementById('schedule-list').innerHTML = '<p>Schedule management feature coming soon...</p>';
}

async function loadStoryboard() {
    // Implementation will be added
    document.getElementById('storyboard-list').innerHTML = '<p>Storyboard feature coming soon...</p>';
}

// Script functions
function editScript(scriptId) {
    window.location.href = `/script-editor.html?script_id=${scriptId}`;
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
}
