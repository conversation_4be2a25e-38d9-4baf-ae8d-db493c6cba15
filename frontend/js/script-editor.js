// Script Editor JavaScript - Keyboard Capture

// Script Editor functionality
class ScriptEditor {
    constructor() {
        this.currentProject = null;
        this.currentScript = null;
        this.elements = [];
        this.selectedElementIndex = -1;
        this.cursorPosition = 0;
        this.projectCharacters = [];
        this.currentElementType = 'action';
        this.currentText = '';
        this.init().catch(error => {
            console.error('Error initializing script editor:', error);
            this.showError('Failed to initialize script editor');
        });
    }

    async init() {
        this.setupEventListeners();
        this.loadElementTypes();
        await this.loadScript();
    }

    loadElementTypes() {
        // Element types are already defined in the HTML, but we can initialize any dynamic behavior here
        const elementSelector = document.getElementById('element-type-select');
        if (elementSelector) {
            // Set default value
            elementSelector.value = this.currentElementType;

            // Add event listener for element type changes
            elementSelector.addEventListener('change', (e) => {
                this.currentElementType = e.target.value;
                this.handleElementTypeChange(e.target.value);
            });
        }
    }

    handleElementTypeChange(elementType) {
        // Show/hide relevant controls based on element type
        const characterSelection = document.getElementById('character-selection');
        const shotOptions = document.getElementById('shot-options');

        // Hide all optional controls first
        if (characterSelection) characterSelection.classList.add('hidden');
        if (shotOptions) shotOptions.classList.add('hidden');

        // Show relevant controls based on element type
        switch (elementType) {
            case 'character':
            case 'dialog':
                if (characterSelection) {
                    characterSelection.classList.remove('hidden');
                    this.renderCharacterList();
                }
                break;
            case 'shot':
                if (shotOptions) shotOptions.classList.remove('hidden');
                break;
        }
    }

    async loadScript() {
        // Get script ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const scriptId = urlParams.get('script_id');

        if (!scriptId) {
            this.showError('No script ID provided');
            return;
        }

        try {
            // Load script data
            const response = await apiCall(`/scripts/${scriptId}`);
            this.currentScript = response.script;

            // Update UI with script info
            document.getElementById('script-title').textContent = this.currentScript.title;
            document.getElementById('script-project').textContent = `Project: ${this.currentScript.project_name}`;

            // Load script elements
            await this.loadScriptElements(scriptId);

            // Load project characters for character selection
            await this.loadProjectCharacters(this.currentScript.project_id);

        } catch (error) {
            console.error('Error loading script:', error);
            this.showError('Failed to load script: ' + error.message);
        }
    }

    async loadScriptElements(scriptId) {
        try {
            const response = await apiCall(`/scripts/${scriptId}/elements`);
            this.elements = response.elements || [];

            // Update element count
            document.getElementById('element-count').textContent = `Elements: ${this.elements.length}`;

            // Render script elements
            this.renderScriptElements();

            // If no elements exist, create a default action element
            if (this.elements.length === 0) {
                this.createInitialElement();
            }

        } catch (error) {
            console.error('Error loading script elements:', error);
            // If elements don't exist yet, create initial element
            this.createInitialElement();
        }
    }

    async loadProjectCharacters(projectId) {
        try {
            const response = await apiCall(`/cast?project_id=${projectId}`);
            this.projectCharacters = response.cast || [];
        } catch (error) {
            console.error('Error loading project characters:', error);
            this.projectCharacters = [];
        }
    }

    renderScriptElements() {
        const container = document.getElementById('script-content');
        container.innerHTML = '';

        this.elements.forEach((element, index) => {
            const elementDiv = this.createElement(element.element_type, element.content);
            elementDiv.dataset.elementId = element.id;
            container.appendChild(elementDiv);
        });

        // Select first element if available
        if (this.elements.length > 0) {
            const firstElement = container.firstElementChild;
            if (firstElement) {
                this.selectElement(0, firstElement);
            }
        }
    }

    createInitialElement() {
        const container = document.getElementById('script-content');
        const initialElement = this.createElement('action', '');
        container.appendChild(initialElement);

        // Select the initial element
        this.selectElement(0, initialElement);
        this.positionCursorAtStart(initialElement);

        // Focus the script document
        document.getElementById('script-document').focus();
    }

    showError(message) {
        document.getElementById('script-title').textContent = 'Error';
        document.getElementById('script-project').textContent = message;
        console.error(message);
    }

    setupEventListeners() {
        const editor = document.getElementById('script-document');
        if (editor) {
            editor.addEventListener('click', (e) => this.handleEditorClick(e));
            editor.addEventListener('keydown', (e) => this.handleKeyDown(e));
            editor.addEventListener('input', (e) => this.handleInput(e));
        }

        // Element type selector
        const elementSelector = document.getElementById('element-selector');
        if (elementSelector) {
            elementSelector.addEventListener('change', (e) => this.changeElementType(e));
        }
    }

    handleEditorClick(e) {
        const clickedElement = e.target.closest('.script-element');
        if (clickedElement) {
            const index = Array.from(clickedElement.parentNode.children).indexOf(clickedElement);
            this.selectElement(index, clickedElement);
            this.setCursorPosition(e, clickedElement);
        }
    }

    selectElement(index, element) {
        // Remove previous selection
        document.querySelectorAll('.script-element.selected').forEach(el => {
            el.classList.remove('selected');
        });

        // Add selection to current element
        element.classList.add('selected');
        this.selectedElementIndex = index;

        // Position element selector at the top of the selected element
        this.positionElementSelector(element);
        
        // Update element selector dropdown
        const selector = document.getElementById('element-selector');
        if (selector) {
            selector.value = element.dataset.elementType || 'action';
            selector.style.display = 'block';
        }
    }

    positionElementSelector(element) {
        const selector = document.getElementById('element-selector');
        if (!selector) return;

        const rect = element.getBoundingClientRect();
        const editorRect = document.getElementById('script-editor').getBoundingClientRect();
        
        // Position selector at the top-left of the element, relative to the editor
        selector.style.position = 'absolute';
        selector.style.top = `${rect.top - editorRect.top - 2}px`;
        selector.style.left = `${rect.left - editorRect.left}px`;
        selector.style.zIndex = '1000';
    }

    setCursorPosition(e, element) {
        // Create a range and selection to position cursor
        const range = document.createRange();
        const selection = window.getSelection();
        
        // Find the text node and position within it
        const textNode = this.findTextNodeAtPoint(element, e.clientX, e.clientY);
        if (textNode) {
            const offset = this.getTextOffset(textNode, e.clientX, e.clientY);
            range.setStart(textNode, offset);
            range.collapse(true);
            selection.removeAllRanges();
            selection.addRange(range);
            this.cursorPosition = offset;
        }
    }

    findTextNodeAtPoint(element, x, y) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            const range = document.createRange();
            range.selectNodeContents(node);
            const rect = range.getBoundingClientRect();
            if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
                return node;
            }
        }
        return element.firstChild || element;
    }

    getTextOffset(textNode, x, y) {
        const text = textNode.textContent;
        let offset = 0;
        
        for (let i = 0; i <= text.length; i++) {
            const range = document.createRange();
            range.setStart(textNode, 0);
            range.setEnd(textNode, i);
            const rect = range.getBoundingClientRect();
            
            if (x <= rect.right) {
                offset = i;
                break;
            }
        }
        
        return Math.min(offset, text.length);
    }

    handleInput(e) {
        // Handle text input in script elements
        const selectedElement = document.querySelector('.script-element.selected');
        if (selectedElement) {
            this.currentText = selectedElement.textContent;
        }
    }

    handleKeyDown(e) {
        const selectedElement = document.querySelector('.script-element.selected');
        if (!selectedElement) return;

        switch (e.key) {
            case 'Enter':
                e.preventDefault();
                this.handleEnterKey(selectedElement);
                break;
            case 'Backspace':
                this.handleBackspace(e, selectedElement);
                break;
            case 'Delete':
                this.handleDelete(e, selectedElement);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.moveToElement(-1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.moveToElement(1);
                break;
            case 'ArrowLeft':
                this.handleArrowLeft(e, selectedElement);
                break;
            case 'ArrowRight':
                this.handleArrowRight(e, selectedElement);
                break;
            case 'Tab':
                e.preventDefault();
                this.cycleElementType();
                break;
        }
    }

    handleBackspace(e, element) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            
            // If cursor is at the beginning of the element and element is empty or cursor at start
            if (range.startOffset === 0 && range.collapsed) {
                e.preventDefault();
                
                // If element is empty or only whitespace, merge with previous element
                if (!element.textContent.trim()) {
                    this.mergeWithPreviousElement(element);
                } else {
                    // Move cursor to end of previous element
                    this.moveToPreviousElement();
                }
            }
        }
    }

    handleDelete(e, element) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const textContent = element.textContent;
            
            // If cursor is at the end of the element
            if (range.startOffset === textContent.length && range.collapsed) {
                e.preventDefault();
                this.mergeWithNextElement(element);
            }
        }
    }

    handleArrowLeft(e, element) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            
            // If at the beginning of the element, move to previous element
            if (range.startOffset === 0 && range.collapsed) {
                e.preventDefault();
                this.moveToPreviousElement();
            }
        }
    }

    handleArrowRight(e, element) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const textContent = element.textContent;
            
            // If at the end of the element, move to next element
            if (range.startOffset === textContent.length && range.collapsed) {
                e.preventDefault();
                this.moveToNextElement();
            }
        }
    }

    handleEnterKey(selectedElement) {
        // Create new element after current one
        const newElement = this.createElement('action', '');
        const nextElement = selectedElement.nextElementSibling;
        
        if (nextElement) {
            selectedElement.parentNode.insertBefore(newElement, nextElement);
        } else {
            selectedElement.parentNode.appendChild(newElement);
        }
        
        // Select the new element and position cursor at the beginning
        this.selectElement(this.selectedElementIndex + 1, newElement);
        this.positionCursorAtStart(newElement);
    }

    moveToElement(direction) {
        const elements = document.querySelectorAll('.script-element');
        let newIndex = this.selectedElementIndex + direction;
        
        if (newIndex >= 0 && newIndex < elements.length) {
            this.selectElement(newIndex, elements[newIndex]);
            this.positionCursorAtStart(elements[newIndex]);
        }
    }

    moveToPreviousElement() {
        const elements = document.querySelectorAll('.script-element');
        if (this.selectedElementIndex > 0) {
            const prevElement = elements[this.selectedElementIndex - 1];
            this.selectElement(this.selectedElementIndex - 1, prevElement);
            this.positionCursorAtEnd(prevElement);
        }
    }

    moveToNextElement() {
        const elements = document.querySelectorAll('.script-element');
        if (this.selectedElementIndex < elements.length - 1) {
            const nextElement = elements[this.selectedElementIndex + 1];
            this.selectElement(this.selectedElementIndex + 1, nextElement);
            this.positionCursorAtStart(nextElement);
        }
    }

    mergeWithPreviousElement(element) {
        const prevElement = element.previousElementSibling;
        if (prevElement && prevElement.classList.contains('script-element')) {
            const prevText = prevElement.textContent;
            const currentText = element.textContent;
            
            prevElement.textContent = prevText + currentText;
            element.remove();
            
            this.selectElement(this.selectedElementIndex - 1, prevElement);
            this.positionCursorAt(prevElement, prevText.length);
        }
    }

    mergeWithNextElement(element) {
        const nextElement = element.nextElementSibling;
        if (nextElement && nextElement.classList.contains('script-element')) {
            const currentText = element.textContent;
            const nextText = nextElement.textContent;
            
            element.textContent = currentText + nextText;
            nextElement.remove();
            
            this.positionCursorAt(element, currentText.length);
        }
    }

    positionCursorAtStart(element) {
        this.positionCursorAt(element, 0);
    }

    positionCursorAtEnd(element) {
        this.positionCursorAt(element, element.textContent.length);
    }

    positionCursorAt(element, offset) {
        const range = document.createRange();
        const selection = window.getSelection();
        
        const textNode = element.firstChild || element;
        const maxOffset = textNode.textContent ? textNode.textContent.length : 0;
        const safeOffset = Math.min(offset, maxOffset);
        
        range.setStart(textNode, safeOffset);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
        
        element.focus();
        this.cursorPosition = safeOffset;
    }

    cycleElementType() {
        const types = ['action', 'character', 'dialogue', 'parenthetical', 'transition', 'shot'];
        const selectedElement = document.querySelector('.script-element.selected');
        if (!selectedElement) return;

        const currentType = selectedElement.dataset.elementType || 'action';
        const currentIndex = types.indexOf(currentType);
        const nextIndex = (currentIndex + 1) % types.length;
        const nextType = types[nextIndex];

        this.changeElementTypeDirectly(selectedElement, nextType);
    }

    changeElementType(e) {
        const selectedElement = document.querySelector('.script-element.selected');
        if (selectedElement) {
            this.changeElementTypeDirectly(selectedElement, e.target.value);
        }
    }

    changeElementTypeDirectly(element, newType) {
        element.className = `script-element ${newType}`;
        element.dataset.elementType = newType;
        
        // Update selector
        const selector = document.getElementById('element-selector');
        if (selector) {
            selector.value = newType;
        }
    }

    createElement(elementType, content) {
        const element = document.createElement('div');
        element.className = `script-element ${elementType}`;
        element.dataset.elementType = elementType;
        element.textContent = content;
        element.contentEditable = true;
        return element;
    }

    // Removed old navigation and cursor functions - no longer needed

    updateCurrentLine() {
        const selectedElement = document.querySelector('.script-element.selected');
        if (selectedElement) {
            selectedElement.textContent = this.currentText;
        }
    }

    // Render character list
    renderCharacterList() {
        const container = document.getElementById('character-list');
        container.innerHTML = '';

        this.projectCharacters.forEach(character => {
            const characterDiv = document.createElement('div');
            characterDiv.className = 'character-item';
            characterDiv.dataset.name = character.name;
            characterDiv.textContent = character.name;

            characterDiv.addEventListener('click', () => {
                // Remove previous selection
                document.querySelectorAll('.character-item.selected').forEach(el => {
                    el.classList.remove('selected');
                });

                // Select this character
                characterDiv.classList.add('selected');

                // Auto-populate current text with character name if element type is character
                if (this.currentElementType === 'character') {
                    this.currentText = character.name.toUpperCase();
                    this.updateCurrentLine();
                    document.getElementById('script-document').focus();
                }
            });

            container.appendChild(characterDiv);
        });
    }

    // Save script
    async saveScript() {
        try {
            // Script elements are saved individually as they're created
            // This could be used for saving script metadata
            alert('Script saved successfully!');
        } catch (error) {
            console.error('Error saving script:', error);
            alert('Error saving script: ' + error.message);
        }
    }

    // Character creation functions removed - not needed for script editor

    async createCharacter() {
        const form = document.getElementById('create-character-form');
        const formData = new FormData(form);

        const characterData = {
            project_id: this.currentScript.project_id,
            name: formData.get('name'),
            character_name: formData.get('name'),
            email: formData.get('email') || '',
            phone: formData.get('phone') || '',
            notes: formData.get('description') || ''
        };

        try {
            const response = await apiCall('/cast', 'POST', characterData);

            // Add to local array
            this.projectCharacters.push({
                id: response.cast_id,
                ...characterData
            });

            // Re-render character list
            this.renderCharacterList();

            // Close modal
            hideCreateCharacterModal();

            alert('Character created successfully!');
        } catch (error) {
            console.error('Error creating character:', error);
            alert('Error creating character: ' + error.message);
        }
    }

    // Storyboard timeline functions removed - not needed for script editor



    // Create storyboard panel
    async createStoryboardPanel(sceneNumber, panelNumber) {
        const description = prompt('Panel description:');
        if (!description) return;

        const panelData = {
            scene_number: sceneNumber,
            panel_number: panelNumber,
            description: description
        };

        try {
            await apiCall(`/scripts/${scriptId}/storyboard-panels`, 'POST', panelData);
            alert('Storyboard panel created successfully!');
            renderStoryboardTimeline(); // Refresh timeline
        } catch (error) {
            console.error('Error creating storyboard panel:', error);
            alert('Error creating storyboard panel: ' + error.message);
        }
    }

    // Logout function
    async logout() {
        try {
            await apiCall('/auth/logout', 'POST');
            window.location.href = '/login.html';
        } catch (error) {
            console.error('Logout error:', error);
            window.location.href = '/login.html';
        }
    }
}
