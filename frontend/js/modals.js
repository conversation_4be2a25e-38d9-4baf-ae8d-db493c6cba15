// Modal management functions

// Create modal container
function createModal(title, content, actions = '') {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                ${actions}
            </div>
        </div>
    `;
    
    // Add modal styles
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .modal {
            background: white;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        .modal-body {
            padding: 1rem;
        }
        .modal-footer {
            padding: 1rem;
            border-top: 1px solid #eee;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }
        .modal-form .form-group {
            margin-bottom: 1rem;
        }
        .modal-form label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .modal-form input,
        .modal-form textarea,
        .modal-form select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        .modal-form textarea {
            min-height: 100px;
            resize: vertical;
        }
    `;
    
    if (!document.querySelector('style[data-modal-styles]')) {
        style.setAttribute('data-modal-styles', 'true');
        document.head.appendChild(style);
    }
    
    return modal;
}

// Show modal
function showModal(modal) {
    document.getElementById('modal-container').appendChild(modal);
    
    // Close on overlay click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Close on escape key
    document.addEventListener('keydown', function escapeHandler(e) {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escapeHandler);
        }
    });
}

// Close modal
function closeModal() {
    const container = document.getElementById('modal-container');
    container.innerHTML = '';
}

// Project modals
function showCreateProjectModal() {
    const content = `
        <form class="modal-form" id="createProjectForm">
            <div class="form-group">
                <label for="projectName">Project Name *</label>
                <input type="text" id="projectName" name="name" required>
            </div>
            <div class="form-group">
                <label for="projectDescription">Description</label>
                <textarea id="projectDescription" name="description" placeholder="Optional project description"></textarea>
            </div>
        </form>
    `;
    
    const actions = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-primary" onclick="handleCreateProject()">Create Project</button>
    `;
    
    const modal = createModal('Create New Project', content, actions);
    showModal(modal);
}

async function handleCreateProject() {
    const form = document.getElementById('createProjectForm');
    const formData = new FormData(form);
    
    const projectData = {
        name: formData.get('name'),
        description: formData.get('description')
    };
    
    try {
        await createProject(projectData);
        closeModal();
        showNotification('Project created successfully!', 'success');
        await loadProjects();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

function editProject(projectId) {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;
    
    const content = `
        <form class="modal-form" id="editProjectForm">
            <div class="form-group">
                <label for="editProjectName">Project Name *</label>
                <input type="text" id="editProjectName" name="name" value="${escapeHtml(project.name)}" required>
            </div>
            <div class="form-group">
                <label for="editProjectDescription">Description</label>
                <textarea id="editProjectDescription" name="description">${escapeHtml(project.description || '')}</textarea>
            </div>
        </form>
    `;
    
    const actions = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-primary" onclick="handleEditProject(${projectId})">Update Project</button>
    `;
    
    const modal = createModal('Edit Project', content, actions);
    showModal(modal);
}

async function handleEditProject(projectId) {
    const form = document.getElementById('editProjectForm');
    const formData = new FormData(form);
    
    const projectData = {
        name: formData.get('name'),
        description: formData.get('description')
    };
    
    try {
        await updateProject(projectId, projectData);
        closeModal();
        showNotification('Project updated successfully!', 'success');
        await loadProjects();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

async function deleteProject(projectId) {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;
    
    if (confirm(`Are you sure you want to delete "${project.name}"? This action cannot be undone.`)) {
        try {
            await deleteProjectApi(projectId);
            showNotification('Project deleted successfully!', 'success');
            await loadProjects();
        } catch (error) {
            showNotification(error.message, 'error');
        }
    }
}

// Script modals
function showCreateScriptModal() {
    const projectId = document.getElementById('script-project-select').value;
    if (!projectId) {
        showNotification('Please select a project first', 'error');
        return;
    }
    
    const content = `
        <form class="modal-form" id="createScriptForm">
            <div class="form-group">
                <label for="scriptTitle">Script Title *</label>
                <input type="text" id="scriptTitle" name="title" required>
            </div>
            <div class="form-group">
                <label for="scriptFormat">Format</label>
                <select id="scriptFormat" name="format_type">
                    <option value="screenplay">Screenplay</option>
                    <option value="teleplay">Teleplay</option>
                    <option value="stage_play">Stage Play</option>
                    <option value="documentary">Documentary</option>
                </select>
            </div>
            <div class="form-group">
                <label for="scriptContent">Content</label>
                <textarea id="scriptContent" name="content" placeholder="Start writing your script here..."></textarea>
            </div>
        </form>
    `;
    
    const actions = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-primary" onclick="handleCreateScript(${projectId})">Create Script</button>
    `;
    
    const modal = createModal('Create New Script', content, actions);
    showModal(modal);
}

async function handleCreateScript(projectId) {
    const form = document.getElementById('createScriptForm');
    const formData = new FormData(form);
    
    const scriptData = {
        project_id: projectId,
        title: formData.get('title'),
        format_type: formData.get('format_type'),
        content: formData.get('content')
    };
    
    try {
        await createScript(scriptData);
        closeModal();
        showNotification('Script created successfully!', 'success');
        await loadScripts();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

// Cast modals
function showCreateCastModal() {
    const projectId = document.getElementById('cast-project-select').value;
    if (!projectId) {
        showNotification('Please select a project first', 'error');
        return;
    }

    const content = `
        <form class="modal-form" id="createCastForm">
            <div class="form-group">
                <label for="castName">Name *</label>
                <input type="text" id="castName" name="name" required>
            </div>
            <div class="form-group">
                <label for="characterName">Character Name</label>
                <input type="text" id="characterName" name="character_name">
            </div>
            <div class="form-group">
                <label for="castEmail">Email</label>
                <input type="email" id="castEmail" name="email">
            </div>
            <div class="form-group">
                <label for="castPhone">Phone</label>
                <input type="tel" id="castPhone" name="phone">
            </div>
            <div class="form-group">
                <label for="castNotes">Notes</label>
                <textarea id="castNotes" name="notes" placeholder="Additional notes about the cast member"></textarea>
            </div>
        </form>
    `;

    const actions = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-primary" onclick="handleCreateCast(${projectId})">Add Cast Member</button>
    `;

    const modal = createModal('Add Cast Member', content, actions);
    showModal(modal);
}

async function handleCreateCast(projectId) {
    const form = document.getElementById('createCastForm');
    const formData = new FormData(form);

    const castData = {
        project_id: projectId,
        name: formData.get('name'),
        character_name: formData.get('character_name'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        notes: formData.get('notes')
    };

    try {
        await createCastMember(castData);
        closeModal();
        showNotification('Cast member added successfully!', 'success');
        await loadCast();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

// Crew modals
function showCreateCrewModal() {
    const projectId = document.getElementById('crew-project-select').value;
    if (!projectId) {
        showNotification('Please select a project first', 'error');
        return;
    }

    const content = `
        <form class="modal-form" id="createCrewForm">
            <div class="form-group">
                <label for="crewName">Name *</label>
                <input type="text" id="crewName" name="name" required>
            </div>
            <div class="form-group">
                <label for="crewRole">Role *</label>
                <input type="text" id="crewRole" name="role" required placeholder="e.g., Director, Cinematographer, Sound Engineer">
            </div>
            <div class="form-group">
                <label for="crewDepartment">Department</label>
                <select id="crewDepartment" name="department">
                    <option value="">Select department...</option>
                    <option value="Direction">Direction</option>
                    <option value="Camera">Camera</option>
                    <option value="Sound">Sound</option>
                    <option value="Lighting">Lighting</option>
                    <option value="Art">Art</option>
                    <option value="Costume">Costume</option>
                    <option value="Makeup">Makeup</option>
                    <option value="Production">Production</option>
                    <option value="Post-Production">Post-Production</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label for="crewEmail">Email</label>
                <input type="email" id="crewEmail" name="email">
            </div>
            <div class="form-group">
                <label for="crewPhone">Phone</label>
                <input type="tel" id="crewPhone" name="phone">
            </div>
            <div class="form-group">
                <label for="crewNotes">Notes</label>
                <textarea id="crewNotes" name="notes" placeholder="Additional notes about the crew member"></textarea>
            </div>
        </form>
    `;

    const actions = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-primary" onclick="handleCreateCrew(${projectId})">Add Crew Member</button>
    `;

    const modal = createModal('Add Crew Member', content, actions);
    showModal(modal);
}

async function handleCreateCrew(projectId) {
    const form = document.getElementById('createCrewForm');
    const formData = new FormData(form);

    const crewData = {
        project_id: projectId,
        name: formData.get('name'),
        role: formData.get('role'),
        department: formData.get('department'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        notes: formData.get('notes')
    };

    try {
        await createCrewMember(crewData);
        closeModal();
        showNotification('Crew member added successfully!', 'success');
        await loadCrew();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

// Placeholder functions for other modals
function showCreateShotModal() {
    showNotification('Shot list creation coming soon!', 'info');
}

function showCreateScheduleModal() {
    showNotification('Schedule item creation coming soon!', 'info');
}

function showCreateStoryboardModal() {
    showNotification('Storyboard panel creation coming soon!', 'info');
}

// editScript function is defined in app.js - removed duplicate

function deleteScript(scriptId) {
    showNotification('Script deletion coming soon!', 'info');
}

function editCastMember(castId) {
    showNotification('Cast member editing coming soon!', 'info');
}

function deleteCastMember(castId) {
    showNotification('Cast member deletion coming soon!', 'info');
}

function editCrewMember(crewId) {
    showNotification('Crew member editing coming soon!', 'info');
}

function deleteCrewMember(crewId) {
    showNotification('Crew member deletion coming soon!', 'info');
}
