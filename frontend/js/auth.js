// Authentication handling
// Detect environment and set API base URL
const API_BASE = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')
    ? 'http://localhost:5000'
    : 'https://api.morethanscripts.com';

// Show/hide forms
function showLogin() {
    document.getElementById('loginForm').classList.remove('hidden');
    document.getElementById('registerForm').classList.add('hidden');
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-btn')[0].classList.add('active');
    clearMessage();
}

function showRegister() {
    document.getElementById('loginForm').classList.add('hidden');
    document.getElementById('registerForm').classList.remove('hidden');
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-btn')[1].classList.add('active');
    clearMessage();
}

// Message handling
function showMessage(message, type = 'error') {
    const messageEl = document.getElementById('message');
    messageEl.textContent = message;
    messageEl.className = `message ${type}`;
}

function clearMessage() {
    const messageEl = document.getElementById('message');
    messageEl.className = 'message';
    messageEl.textContent = '';
}

// API calls
async function apiCall(endpoint, method = 'GET', data = null) {
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(`${API_BASE}${endpoint}`, options);
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'An error occurred');
        }
        
        return result;
    } catch (error) {
        throw error;
    }
}

// Login form handler
document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        const result = await apiCall('/auth/login', 'POST', data);
        showMessage('Login successful!', 'success');
        
        // Redirect to main app immediately
        window.location.href = '/index.html';
        
    } catch (error) {
        showMessage(error.message);
    }
});

// Register form handler
document.getElementById('registerForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    
    if (password !== confirmPassword) {
        showMessage('Passwords do not match');
        return;
    }
    
    const data = {
        username: formData.get('username'),
        email: formData.get('email'),
        password: password
    };
    
    try {
        const result = await apiCall('/auth/register', 'POST', data);
        showMessage('Registration successful! Please login.', 'success');
        
        // Switch to login form
        setTimeout(() => {
            showLogin();
        }, 1500);
        
    } catch (error) {
        showMessage(error.message);
    }
});

// Check if user is already logged in
async function checkAuth() {
    try {
        const result = await apiCall('/auth/me');
        // User is logged in, redirect to main app
        window.location.href = '/index.html';
    } catch (error) {
        // User is not logged in, stay on login page
    }
}

// Check authentication on page load
document.addEventListener('DOMContentLoaded', checkAuth);
