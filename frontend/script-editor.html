<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Script Editor - More Than <PERSON></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/script-editor.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">More Than Scripts</h1>
                <nav class="nav">
                    <button class="nav-btn" onclick="window.location.href='/index.html'">← Back to Dashboard</button>
                    <button class="nav-btn" id="save-script-btn">Save Script</button>
                </nav>
                <div class="user-menu">
                    <button class="btn btn-secondary" onclick="logout()">Logout</button>
                </div>
            </div>
        </header>

        <main class="main-content">
            <!-- Script Info -->
            <div class="script-info">
                <h2 id="script-title">Loading...</h2>
                <div class="script-meta">
                    <span id="script-project">Project: Loading...</span>
                    <span id="element-count">Elements: 0</span>
                </div>
            </div>

            <!-- Script Editor -->
            <div class="script-editor">
                <div class="editor-container">
                    <!-- Script Document -->
                    <div class="script-document" id="script-document" tabindex="0">
                        <div id="script-content" class="script-content">
                            <!-- Script lines will be rendered here -->
                        </div>
                        <div class="current-line" id="current-line">
                            <div class="line-content" id="line-content"></div>
                            <div class="cursor" id="cursor">|</div>
                        </div>
                    </div>

                    <!-- Controls Panel -->
                    <div class="controls-panel">
                        <div class="element-controls">
                            <label for="element-type-select">Element Type:</label>
                            <select id="element-type-select" class="element-type-select">
                                <option value="action">Action</option>
                                <option value="character">Character</option>
                                <option value="dialog">Dialog</option>
                                <option value="parenthetical">Parenthetical</option>
                                <option value="transition">Transition</option>
                                <option value="shot">Shot</option>
                                <option value="act">Act</option>
                                <option value="scene">Scene</option>
                            </select>

                            <!-- Character Selection -->
                            <div id="character-selection" class="character-selection hidden">
                                <label>Select Character:</label>
                                <div id="character-list" class="character-list">
                                    <!-- Characters will be loaded here -->
                                </div>
                            </div>

                            <!-- Shot Options -->
                            <div id="shot-options" class="shot-options hidden">
                                <label>Location:</label>
                                <select id="shot-location">
                                    <option value="INT.">INT.</option>
                                    <option value="EXT.">EXT.</option>
                                </select>
                                <label>Time:</label>
                                <select id="shot-time">
                                    <option value="DAY">DAY</option>
                                    <option value="NIGHT">NIGHT</option>
                                    <option value="DAWN">DAWN</option>
                                    <option value="DUSK">DUSK</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>



    <script src="js/api.js"></script>
    <script src="js/script-editor.js"></script>
</body>
</html>
