# More Than Scripts - API Documentation

## Base URL
All API endpoints are prefixed with `/api`

## Authentication
The API uses session-based authentication. After logging in, the session cookie is automatically included in subsequent requests.

## Response Format
All responses are in JSON format with the following structure:

**Success Response:**
```json
{
  "message": "Success message",
  "data": { ... }
}
```

**Error Response:**
```json
{
  "error": "Error message"
}
```

## Authentication Endpoints

### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "string (min 3 chars)",
  "email": "string (valid email)",
  "password": "string (min 6 chars)"
}
```

**Response:** `201 Created`
```json
{
  "message": "User registered successfully",
  "user_id": 1
}
```

### POST /api/auth/login
Login with existing credentials.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:** `200 OK`
```json
{
  "message": "Login successful",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

### POST /api/auth/logout
Logout and invalidate session.

**Response:** `200 OK`
```json
{
  "message": "Logged out successfully"
}
```

### GET /api/auth/me
Get current user information.

**Response:** `200 OK`
```json
{
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

## Project Management

### GET /api/projects
Get all projects for the authenticated user.

**Response:** `200 OK`
```json
{
  "projects": [
    {
      "id": 1,
      "name": "My Film Project",
      "description": "A great film",
      "created_at": "2023-01-01 12:00:00",
      "updated_at": "2023-01-01 12:00:00"
    }
  ]
}
```

### POST /api/projects
Create a new project.

**Request Body:**
```json
{
  "name": "string (required)",
  "description": "string (optional)"
}
```

**Response:** `201 Created`
```json
{
  "message": "Project created successfully",
  "project_id": 1
}
```

### GET /api/projects/{id}
Get a specific project.

**Response:** `200 OK`
```json
{
  "project": {
    "id": 1,
    "name": "My Film Project",
    "description": "A great film",
    "created_at": "2023-01-01 12:00:00",
    "updated_at": "2023-01-01 12:00:00"
  }
}
```

### PUT /api/projects/{id}
Update a project.

**Request Body:**
```json
{
  "name": "string (optional)",
  "description": "string (optional)"
}
```

**Response:** `200 OK`
```json
{
  "message": "Project updated successfully"
}
```

### DELETE /api/projects/{id}
Delete a project and all associated data.

**Response:** `200 OK`
```json
{
  "message": "Project deleted successfully"
}
```

## Script Management

### GET /api/scripts?project_id={id}
Get all scripts for a project.

**Query Parameters:**
- `project_id` (required): The project ID

**Response:** `200 OK`
```json
{
  "scripts": [
    {
      "id": 1,
      "project_id": 1,
      "title": "My Script",
      "content": "FADE IN:\n\n...",
      "format_type": "screenplay",
      "created_at": "2023-01-01 12:00:00",
      "updated_at": "2023-01-01 12:00:00"
    }
  ]
}
```

### POST /api/scripts
Create a new script.

**Request Body:**
```json
{
  "project_id": 1,
  "title": "string (required)",
  "content": "string (optional)",
  "format_type": "string (optional, default: screenplay)"
}
```

**Response:** `201 Created`
```json
{
  "message": "Script created successfully",
  "script_id": 1
}
```

### GET /api/scripts/{id}
Get a specific script.

**Response:** `200 OK`
```json
{
  "script": {
    "id": 1,
    "project_id": 1,
    "title": "My Script",
    "content": "FADE IN:\n\n...",
    "format_type": "screenplay",
    "created_at": "2023-01-01 12:00:00",
    "updated_at": "2023-01-01 12:00:00"
  }
}
```

### PUT /api/scripts/{id}
Update a script.

**Request Body:**
```json
{
  "title": "string (optional)",
  "content": "string (optional)",
  "format_type": "string (optional)"
}
```

**Response:** `200 OK`
```json
{
  "message": "Script updated successfully"
}
```

### DELETE /api/scripts/{id}
Delete a script.

**Response:** `200 OK`
```json
{
  "message": "Script deleted successfully"
}
```

## Cast Management

### GET /api/cast?project_id={id}
Get all cast members for a project.

**Query Parameters:**
- `project_id` (required): The project ID

**Response:** `200 OK`
```json
{
  "cast": [
    {
      "id": 1,
      "project_id": 1,
      "name": "John Doe",
      "character_name": "Main Character",
      "email": "<EMAIL>",
      "phone": "555-0123",
      "notes": "Lead actor",
      "created_at": "2023-01-01 12:00:00"
    }
  ]
}
```

### POST /api/cast
Add a new cast member.

**Request Body:**
```json
{
  "project_id": 1,
  "name": "string (required)",
  "character_name": "string (optional)",
  "email": "string (optional)",
  "phone": "string (optional)",
  "notes": "string (optional)"
}
```

**Response:** `201 Created`
```json
{
  "message": "Cast member added successfully",
  "cast_id": 1
}
```

## Crew Management

### GET /api/crew?project_id={id}
Get all crew members for a project.

**Query Parameters:**
- `project_id` (required): The project ID

**Response:** `200 OK`
```json
{
  "crew": [
    {
      "id": 1,
      "project_id": 1,
      "name": "Jane Smith",
      "role": "Director",
      "email": "<EMAIL>",
      "phone": "555-0124",
      "department": "Direction",
      "notes": "Experienced director",
      "created_at": "2023-01-01 12:00:00"
    }
  ]
}
```

### POST /api/crew
Add a new crew member.

**Request Body:**
```json
{
  "project_id": 1,
  "name": "string (required)",
  "role": "string (required)",
  "email": "string (optional)",
  "phone": "string (optional)",
  "department": "string (optional)",
  "notes": "string (optional)"
}
```

**Response:** `201 Created`
```json
{
  "message": "Crew member added successfully",
  "crew_id": 1
}
```

## Error Codes

- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists (e.g., duplicate username)
- `500 Internal Server Error`: Server error
