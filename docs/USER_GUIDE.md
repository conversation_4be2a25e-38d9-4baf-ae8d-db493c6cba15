# More Than Scripts - User Guide

## Getting Started

### Registration and Login

1. **Registration**: Visit the application and click on the "Register" tab
   - Enter a unique username (minimum 3 characters)
   - Provide a valid email address
   - Create a secure password (minimum 6 characters)
   - Confirm your password
   - Click "Register"

2. **Login**: After registration, switch to the "Login" tab
   - Enter your username and password
   - Click "Login"

### Dashboard Overview

Once logged in, you'll see the main dashboard with navigation tabs:

- **Projects**: Manage your film/video projects
- **Scripts**: Write and edit scripts
- **Shot Lists**: Plan your shots (coming soon)
- **Cast**: Manage cast members
- **Crew**: Manage crew members
- **Schedule**: Plan shooting schedules (coming soon)
- **Storyboard**: Create visual storyboards (coming soon)

## Project Management

### Creating a Project

1. Click on the "Projects" tab
2. Click the "New Project" button
3. Enter a project name (required)
4. Add an optional description
5. Click "Create Project"

### Managing Projects

- **View Projects**: All your projects are displayed as cards showing name, description, and creation date
- **Select Project**: Click "Select" to make a project active for other sections
- **Edit Project**: Click "Edit" to modify project details
- **Delete Project**: Click "Delete" to permanently remove a project (this will delete all associated data)

## Script Writing

### Creating Scripts

1. Go to the "Scripts" tab
2. Select a project from the dropdown menu
3. Click "New Script"
4. Enter a script title
5. Choose the format type (Screenplay, Teleplay, Stage Play, or Documentary)
6. Start writing your script in the content area
7. Click "Create Script"

### Script Formats

The application supports multiple script formats:
- **Screenplay**: Traditional film script format
- **Teleplay**: Television script format
- **Stage Play**: Theater script format
- **Documentary**: Documentary script format

### Script Management

- **View Scripts**: All scripts for the selected project are listed
- **Edit Scripts**: Click "Edit" to modify script content (coming soon)
- **Delete Scripts**: Click "Delete" to remove a script (coming soon)

## Cast Management

### Adding Cast Members

1. Go to the "Cast" tab
2. Select a project from the dropdown menu
3. Click "Add Cast Member"
4. Fill in the cast member details:
   - **Name** (required): Actor's real name
   - **Character Name**: Name of the character they play
   - **Email**: Contact email
   - **Phone**: Contact phone number
   - **Notes**: Additional information
5. Click "Add Cast Member"

### Managing Cast

- **View Cast**: All cast members for the selected project are displayed
- **Contact Information**: Email and phone numbers are shown when provided
- **Character Assignments**: See which character each actor plays
- **Edit/Delete**: Modify or remove cast members (coming soon)

## Crew Management

### Adding Crew Members

1. Go to the "Crew" tab
2. Select a project from the dropdown menu
3. Click "Add Crew Member"
4. Fill in the crew member details:
   - **Name** (required): Person's name
   - **Role** (required): Their job title (e.g., Director, Cinematographer)
   - **Department**: Choose from predefined departments or select "Other"
   - **Email**: Contact email
   - **Phone**: Contact phone number
   - **Notes**: Additional information
5. Click "Add Crew Member"

### Crew Departments

Available departments include:
- Direction
- Camera
- Sound
- Lighting
- Art
- Costume
- Makeup
- Production
- Post-Production
- Other

### Managing Crew

- **View Crew**: All crew members are organized by department and role
- **Contact Information**: Easy access to email and phone contacts
- **Role Clarity**: Clear job titles and department assignments
- **Edit/Delete**: Modify or remove crew members (coming soon)

## Tips and Best Practices

### Project Organization

1. **Use Descriptive Names**: Give your projects clear, descriptive names
2. **Add Descriptions**: Include project descriptions to help identify them later
3. **One Project Per Production**: Create separate projects for different productions

### Script Writing

1. **Save Frequently**: The application auto-saves, but it's good practice to update regularly
2. **Use Standard Formatting**: Follow industry-standard script formatting conventions
3. **Version Control**: Consider keeping backup copies of important scripts

### Cast and Crew Management

1. **Complete Information**: Add as much contact information as possible
2. **Clear Roles**: Be specific about character names and crew roles
3. **Use Notes**: Add important details in the notes field
4. **Regular Updates**: Keep contact information current

### Data Security

1. **Secure Passwords**: Use strong, unique passwords for your account
2. **Regular Backups**: Consider exporting important data regularly
3. **Privacy**: Be mindful of personal information you store

## Troubleshooting

### Common Issues

**Can't Login**: 
- Check username and password spelling
- Ensure account was successfully created
- Try refreshing the page

**Projects Not Loading**:
- Refresh the page
- Check your internet connection
- Try logging out and back in

**Can't Create Items**:
- Ensure you've selected a project first
- Check that all required fields are filled
- Try refreshing the page

### Getting Help

If you encounter issues:
1. Try refreshing the page
2. Log out and log back in
3. Check the browser console for error messages
4. Contact support with specific error details

## Future Features

The following features are planned for future releases:
- Shot list generation from scripts
- Shooting schedule management
- Storyboard creation with image support
- Advanced script editing features
- Export capabilities
- Collaboration tools
- Mobile app support

## Data Privacy

- Each user has their own isolated database
- No data is shared between users
- All data is stored locally on the server
- Regular backups are recommended
